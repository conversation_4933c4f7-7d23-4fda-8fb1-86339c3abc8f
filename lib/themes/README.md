# 主题系统使用指南

本项目采用统一的主题管理系统，所有颜色和样式都通过 `AppTheme` 进行管理，便于未来实现主题切换功能。

## 文件结构

- `app_theme.dart` - 主题配置文件，包含所有颜色定义和主题数据

## 使用方法

### 1. 获取主题颜色

```dart
// 使用预定义主题颜色
Container(
  color: Theme.of(context).colorScheme.surface, // 使用预定义颜色
)

Text(
  '示例文本',
  style: Theme.of(context).textTheme.bodyLarge, // 使用预定义文本样式
)

// 使用特殊用途的自定义颜色
Container(
  color: context.appColors.bloodPressureCard, // 血压卡片颜色
)
```

### 2. 使用预定义的文本样式

```dart
Text(
  '标题文本',
  style: Theme.of(context).textTheme.headlineLarge, // 大标题样式
)

Text(
  '正文内容',
  style: Theme.of(context).textTheme.bodyMedium, // 正文样式
)
```

### 3. 预定义主题颜色（推荐使用）

通过 `Theme.of(context).colorScheme` 访问：
- `onSurface` - 主要文本颜色
- `outlineVariant` - 次要文本颜色  
- `onSurfaceVariant` - 第三级文本颜色
- `outline` - 第四级文本颜色
- `surface` - 表面颜色
- `surfaceContainerHighest` - 卡片背景色

### 特殊用途的自定义颜色

通过 `context.appColors` 访问：
- `backgroundGradient` - 背景渐变色
- `bloodPressureCard` - 血压卡片颜色 (#FFEBF0)
- `bloodSugarCard` - 血糖卡片颜色 (#E4F4FF)
- `heartIcon` - 心脏图标颜色
- `dropletIcon` - 水滴图标颜色

### 4. 可用的文本样式

- `headlineLarge` - 大标题 (32px, bold)
- `headlineMedium` - 中标题 (24px, bold)
- `titleLarge` - 大标题 (18px, w600)
- `titleMedium` - 卡片标题 (14px, w600)
- `displayMedium` - 数据值 (24px, bold)
- `labelMedium` - 标签文本 (14px, w600)
- `labelSmall` - 单位文本 (12px)
- `bodyMedium` - 次要信息 (14px, w500)
- `bodySmall` - 辅助信息 (14px)

## 添加新颜色

**优先使用预定义颜色：**
1. 检查 `ColorScheme` 是否已有合适的颜色
2. 使用 `Theme.of(context).colorScheme.xxx` 访问

**添加特殊用途颜色：**
1. 在 `AppTheme` 类中定义颜色常量
2. 在 `AppColors` 类中添加新属性
3. 更新 `copyWith` 和 `lerp` 方法
4. 在主题配置中添加颜色映射

## 注意事项

- **优先使用** `Theme.of(context).colorScheme` 中的预定义颜色
- **仅在特殊需求时** 使用 `context.appColors` 中的自定义颜色
- 避免在代码中硬编码颜色值
- 使用 `const` 构造函数时，不能使用 `Theme.of(context)`
- 所有颜色都应该通过主题系统管理

## 未来扩展

主题系统已经为以下功能做好准备：
- 深色主题支持
- 多主题切换
- 用户自定义主题
- 动态主题更新