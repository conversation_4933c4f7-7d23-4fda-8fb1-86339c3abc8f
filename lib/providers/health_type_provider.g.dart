// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'health_type_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$healthTypeHash() => r'5901457e391450f9081f21c752692a4d5992d4e1';

/// See also [healthType].
@ProviderFor(healthType)
final healthTypeProvider = AutoDisposeProvider<HealthType>.internal(
  healthType,
  name: r'healthTypeProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$healthTypeHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef HealthTypeRef = AutoDisposeProviderRef<HealthType>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
