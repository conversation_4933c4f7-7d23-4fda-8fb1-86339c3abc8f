import 'package:flutter/material.dart';

/// 应用主题配置
class AppTheme {
  // 私有构造函数，防止实例化
  AppTheme._();

  // 渐变色
  static const List<Color> _backgroundGradientColors = [
    Color(0xFFE6FFE9),
    Color(0xFFF0FFF2),
    Color(0xFFFCFFFD),
    Colors.white,
  ];

  // 记录卡片背景色
  static const Color _bloodPressureCardColor = Color(0xFFFFEBF0);
  static const Color _bloodSugarCardColor = Color(0xFFE4F4FF);

  // 图标颜色
  static const Color _heartIconColor = Colors.pink;
  static const Color _dropletIconColor = Colors.lightBlue;

  /// 获取浅色主题
  static ThemeData get lightTheme {
    const Color primaryColor = Color(0xFF7DBA84);
    const Color primaryTextColor = Color(0xFF333333);

    return ThemeData(
      dividerColor: Color(0xFFD2D4D8),
      colorScheme: ColorScheme.light(primary: primaryColor, secondary: Color(0xFFE4F4E6)),
      useMaterial3: true,
      scaffoldBackgroundColor: Colors.white,
      primaryColor: primaryColor,
      extensions: [
        AppColors(
          backgroundGradient: _backgroundGradientColors,
          bloodPressureCard: _bloodPressureCardColor,
          bloodSugarCard: _bloodSugarCardColor,
          heartIcon: _heartIconColor,
          dropletIcon: _dropletIconColor,
          bottomNavigationBarIcon: Color(0xFF999999),
          bottomNavigationBarIconSelected: primaryColor,
        ),
      ],

      // AppBar主题
      appBarTheme: AppBarTheme(backgroundColor: Colors.transparent, elevation: 0),

      // 文本主题（使用ColorScheme中的颜色）
      textTheme: TextTheme(
        // 大标题
        headlineLarge: TextStyle(fontSize: 32, fontWeight: FontWeight.bold, color: primaryTextColor),
        // 中等标题
        headlineMedium: TextStyle(fontSize: 24, fontWeight: FontWeight.bold, color: primaryTextColor),
        // 小标题
        titleLarge: TextStyle(fontSize: 18, fontWeight: FontWeight.w600, color: primaryTextColor),
        // 卡片标题
        titleMedium: TextStyle(fontSize: 14, fontWeight: FontWeight.w600, color: Color(0xFF424B58)),
        // 数据值
        displayMedium: TextStyle(fontSize: 24, fontWeight: FontWeight.bold, color: primaryTextColor),
        // 标签文本
        labelMedium: TextStyle(fontSize: 14, color: Color(0xFF666666), fontWeight: FontWeight.w600),
        // 单位文本
        labelSmall: TextStyle(fontSize: 12, color: Color(0xFF424B58)),
        // 次要信息
        bodyMedium: TextStyle(fontSize: 14, color: Color(0xFF666666), fontWeight: FontWeight.w500),
        // 辅助信息
        bodySmall: TextStyle(fontSize: 14, color: Color(0xFF999999)),
      ),
    );
  }
}

/// 自定义颜色扩展（仅保留特殊用途的颜色）
class AppColors extends ThemeExtension<AppColors> {
  final List<Color> backgroundGradient;
  final Color bloodPressureCard;
  final Color bloodSugarCard;
  final Color heartIcon;
  final Color dropletIcon;
  final Color bottomNavigationBarIcon;
  final Color bottomNavigationBarIconSelected;

  const AppColors({
    required this.backgroundGradient,
    required this.bloodPressureCard,
    required this.bloodSugarCard,
    required this.heartIcon,
    required this.dropletIcon,
    required this.bottomNavigationBarIcon,
    required this.bottomNavigationBarIconSelected,
  });

  @override
  AppColors copyWith({
    List<Color>? backgroundGradient,
    Color? bloodPressureCard,
    Color? bloodSugarCard,
    Color? heartIcon,
    Color? dropletIcon,
    Color? bottomNavigationBarIcon,
    Color? bottomNavigationBarIconSelected,
  }) {
    return AppColors(
      backgroundGradient: backgroundGradient ?? this.backgroundGradient,
      bloodPressureCard: bloodPressureCard ?? this.bloodPressureCard,
      bloodSugarCard: bloodSugarCard ?? this.bloodSugarCard,
      heartIcon: heartIcon ?? this.heartIcon,
      dropletIcon: dropletIcon ?? this.dropletIcon,
      bottomNavigationBarIcon: bottomNavigationBarIcon ?? this.bottomNavigationBarIcon,
      bottomNavigationBarIconSelected: bottomNavigationBarIconSelected ?? this.bottomNavigationBarIconSelected,
    );
  }

  @override
  AppColors lerp(ThemeExtension<AppColors>? other, double t) {
    if (other is! AppColors) {
      return this;
    }
    return AppColors(
      backgroundGradient: backgroundGradient,
      bloodPressureCard: Color.lerp(bloodPressureCard, other.bloodPressureCard, t)!,
      bloodSugarCard: Color.lerp(bloodSugarCard, other.bloodSugarCard, t)!,
      heartIcon: Color.lerp(heartIcon, other.heartIcon, t)!,
      dropletIcon: Color.lerp(dropletIcon, other.dropletIcon, t)!,
      bottomNavigationBarIcon: Color.lerp(bottomNavigationBarIcon, other.bottomNavigationBarIcon, t)!,
      bottomNavigationBarIconSelected: Color.lerp(
        bottomNavigationBarIconSelected,
        other.bottomNavigationBarIconSelected,
        t,
      )!,
    );
  }
}

/// 主题扩展方法
extension ThemeExtensions on BuildContext {
  AppColors get appColors => Theme.of(this).extension<AppColors>()!;
}
