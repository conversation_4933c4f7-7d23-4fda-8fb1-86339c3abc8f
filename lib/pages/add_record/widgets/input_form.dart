import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:health_diary/types/health_types.dart';

import 'blood_pressure_form.dart';
import 'blood_sugar_form.dart';

class InputForm extends ConsumerWidget {
  const InputForm({super.key, required this.recordType});

  final HealthRecordTypeEnum recordType;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    switch (recordType) {
      case HealthRecordTypeEnum.bloodPressure:
        return const BloodPressureForm();
      case HealthRecordTypeEnum.bloodSugar:
        return const BloodSugarForm();
    }
  }
}
