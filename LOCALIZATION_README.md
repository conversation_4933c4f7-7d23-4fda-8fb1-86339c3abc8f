# 国际化 (i18n) 使用指南

本项目使用 `easy_localization` 库实现国际化功能，支持中文和英文两种语言。

## 📁 项目结构

```
assets/
└── translations/
    ├── zh.json     # 中文翻译
    └── en.json     # 英文翻译

lib/
├── utils/
│   └── localization_helper.dart  # 本地化帮助类
└── pages/
    └── settings/
        └── settings_page.dart     # 语言设置页面
```

## 🚀 快速开始

### 1. 添加新的翻译键值对

在 `assets/translations/` 目录下的 JSON 文件中添加新的键值对：

**zh.json (中文)**
```json
{
  "hello": "你好",
  "welcome": "欢迎"
}
```

**en.json (英文)**
```json
{
  "hello": "Hello",
  "welcome": "Welcome"
}
```

### 2. 在代码中使用翻译

```dart
import 'package:easy_localization/easy_localization.dart';

// 基本用法
Text('hello'.tr())  // 显示 "你好" 或 "Hello"

// 带参数的翻译
Text('welcome_user'.tr(args: ['张三']))  // 欢迎，张三！

// 命名参数
Text('user_info'.tr(namedArgs: {'name': '张三', 'age': '25'}))
```

### 3. 切换语言

```dart
// 切换到中文
context.setLocale(const Locale('zh'));

// 切换到英文
context.setLocale(const Locale('en'));

// 使用帮助类
LocalizationHelper.switchToChinese(context);
LocalizationHelper.switchToEnglish(context);
```

### 4. 获取当前语言信息

```dart
// 获取当前语言代码
String langCode = context.locale.languageCode;  // 'zh' 或 'en'

// 获取当前语言名称
String langName = LocalizationHelper.getCurrentLanguageName(context);

// 检查当前语言
bool isChinese = LocalizationHelper.isChinese(context);
bool isEnglish = LocalizationHelper.isEnglish(context);
```

## 🛠️ 开发指南

### 添加新语言支持

1. 在 `assets/translations/` 目录下创建新的语言文件，如 `ja.json`
2. 在 `main.dart` 中添加新的支持语言：
   ```dart
   supportedLocales: const [
     Locale('zh'), 
     Locale('en'),
     Locale('ja'),  // 新增日语支持
   ],
   ```
3. 更新 `LocalizationHelper` 类中的相关方法

### 最佳实践

1. **键名规范**：使用下划线分隔的小写字母，如 `user_profile`、`settings_page`
2. **文件组织**：按功能模块组织翻译键，如：
   ```json
   {
     "home_page_title": "首页",
     "home_good_morning": "早上好",
     "settings_language": "语言设置",
     "settings_theme": "主题设置"
   }
   ```
3. **参数使用**：对于动态内容，使用参数而不是字符串拼接
4. **测试**：确保所有语言的翻译都完整且准确

### 常用翻译键

| 键名 | 中文 | 英文 |
|------|------|------|
| `app_title` | 健康记录 | Health Diary |
| `good_morning` | 早上好 | Good Morning |
| `today_health` | 今日健康 | Today's Health |
| `blood_pressure` | 血压 | Blood Pressure |
| `blood_sugar` | 血糖 | Blood Sugar |
| `recent_records` | 最近记录 | Recent Records |
| `settings` | 设置 | Settings |
| `language` | 语言 | Language |
| `cancel` | 取消 | Cancel |

## 🔧 故障排除

### 常见问题

1. **翻译不生效**
   - 确保已导入 `easy_localization`
   - 检查 JSON 文件格式是否正确
   - 运行 `flutter pub get` 重新加载依赖

2. **语言切换后界面未更新**
   - 确保在 `MaterialApp` 中正确配置了 `localizationsDelegates`、`supportedLocales` 和 `locale`

3. **找不到翻译键**
   - 检查键名是否正确
   - 确保所有语言文件都包含该键
   - 使用 `fallbackLocale` 作为备选

### 调试技巧

```dart
// 打印当前语言信息
print('Current locale: ${context.locale}');
print('Supported locales: ${context.supportedLocales}');
print('Fallback locale: ${context.fallbackLocale}');
```

## 📱 用户体验

- 应用会根据系统语言自动选择合适的语言
- 用户可以在设置页面手动切换语言
- 语言设置会自动保存，下次启动时保持用户选择
- 支持实时语言切换，无需重启应用