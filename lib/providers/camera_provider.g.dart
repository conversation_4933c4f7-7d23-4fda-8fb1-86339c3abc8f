// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'camera_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$cameraControllerNotifierHash() =>
    r'96900be2aee182f13aed7254baf3b65a8e0df682';

/// See also [CameraControllerNotifier].
@ProviderFor(CameraControllerNotifier)
final cameraControllerNotifierProvider =
    AutoDisposeAsyncNotifierProvider<
      CameraControllerNotifier,
      CameraController?
    >.internal(
      CameraControllerNotifier.new,
      name: r'cameraControllerNotifierProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$cameraControllerNotifierHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$CameraControllerNotifier =
    AutoDisposeAsyncNotifier<CameraController?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
