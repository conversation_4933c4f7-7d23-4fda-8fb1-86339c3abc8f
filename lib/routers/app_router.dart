import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../pages/main_frame/main_frame_page.dart';
import '../pages/add_record/add_record_page.dart';

part 'app_router.g.dart';

@TypedGoRoute<MainFrameRoute>(
  path: '/',
  routes: [TypedGoRoute<AddRecordRoute>(path: 'add-record/:type')],
)
@immutable
class MainFrameRoute extends GoRouteData with _$MainFrameRoute {
  const MainFrameRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const MainFramePage();
  }
}

@immutable
class AddRecordRoute extends GoRouteData with _$AddRecordRoute {
  final String type;

  const AddRecordRoute({required this.type});

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return AddRecordPage();
  }
}

final appRouter = GoRouter(routes: $appRoutes);
