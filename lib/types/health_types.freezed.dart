// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'health_types.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$BloodSugarRecord {

 double get value;
/// Create a copy of BloodSugarRecord
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$BloodSugarRecordCopyWith<BloodSugarRecord> get copyWith => _$BloodSugarRecordCopyWithImpl<BloodSugarRecord>(this as BloodSugarRecord, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is BloodSugarRecord&&(identical(other.value, value) || other.value == value));
}


@override
int get hashCode => Object.hash(runtimeType,value);

@override
String toString() {
  return 'BloodSugarRecord(value: $value)';
}


}

/// @nodoc
abstract mixin class $BloodSugarRecordCopyWith<$Res>  {
  factory $BloodSugarRecordCopyWith(BloodSugarRecord value, $Res Function(BloodSugarRecord) _then) = _$BloodSugarRecordCopyWithImpl;
@useResult
$Res call({
 double value
});




}
/// @nodoc
class _$BloodSugarRecordCopyWithImpl<$Res>
    implements $BloodSugarRecordCopyWith<$Res> {
  _$BloodSugarRecordCopyWithImpl(this._self, this._then);

  final BloodSugarRecord _self;
  final $Res Function(BloodSugarRecord) _then;

/// Create a copy of BloodSugarRecord
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? value = null,}) {
  return _then(_self.copyWith(
value: null == value ? _self.value : value // ignore: cast_nullable_to_non_nullable
as double,
  ));
}

}


/// @nodoc


class _BloodSugarRecord implements BloodSugarRecord {
  const _BloodSugarRecord({required this.value});
  

@override final  double value;

/// Create a copy of BloodSugarRecord
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$BloodSugarRecordCopyWith<_BloodSugarRecord> get copyWith => __$BloodSugarRecordCopyWithImpl<_BloodSugarRecord>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _BloodSugarRecord&&(identical(other.value, value) || other.value == value));
}


@override
int get hashCode => Object.hash(runtimeType,value);

@override
String toString() {
  return 'BloodSugarRecord(value: $value)';
}


}

/// @nodoc
abstract mixin class _$BloodSugarRecordCopyWith<$Res> implements $BloodSugarRecordCopyWith<$Res> {
  factory _$BloodSugarRecordCopyWith(_BloodSugarRecord value, $Res Function(_BloodSugarRecord) _then) = __$BloodSugarRecordCopyWithImpl;
@override @useResult
$Res call({
 double value
});




}
/// @nodoc
class __$BloodSugarRecordCopyWithImpl<$Res>
    implements _$BloodSugarRecordCopyWith<$Res> {
  __$BloodSugarRecordCopyWithImpl(this._self, this._then);

  final _BloodSugarRecord _self;
  final $Res Function(_BloodSugarRecord) _then;

/// Create a copy of BloodSugarRecord
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? value = null,}) {
  return _then(_BloodSugarRecord(
value: null == value ? _self.value : value // ignore: cast_nullable_to_non_nullable
as double,
  ));
}


}

/// @nodoc
mixin _$BloodPressureRecord {

 int get systolic; int get diastolic; int? get pulse;
/// Create a copy of BloodPressureRecord
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$BloodPressureRecordCopyWith<BloodPressureRecord> get copyWith => _$BloodPressureRecordCopyWithImpl<BloodPressureRecord>(this as BloodPressureRecord, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is BloodPressureRecord&&(identical(other.systolic, systolic) || other.systolic == systolic)&&(identical(other.diastolic, diastolic) || other.diastolic == diastolic)&&(identical(other.pulse, pulse) || other.pulse == pulse));
}


@override
int get hashCode => Object.hash(runtimeType,systolic,diastolic,pulse);

@override
String toString() {
  return 'BloodPressureRecord(systolic: $systolic, diastolic: $diastolic, pulse: $pulse)';
}


}

/// @nodoc
abstract mixin class $BloodPressureRecordCopyWith<$Res>  {
  factory $BloodPressureRecordCopyWith(BloodPressureRecord value, $Res Function(BloodPressureRecord) _then) = _$BloodPressureRecordCopyWithImpl;
@useResult
$Res call({
 int systolic, int diastolic, int? pulse
});




}
/// @nodoc
class _$BloodPressureRecordCopyWithImpl<$Res>
    implements $BloodPressureRecordCopyWith<$Res> {
  _$BloodPressureRecordCopyWithImpl(this._self, this._then);

  final BloodPressureRecord _self;
  final $Res Function(BloodPressureRecord) _then;

/// Create a copy of BloodPressureRecord
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? systolic = null,Object? diastolic = null,Object? pulse = freezed,}) {
  return _then(_self.copyWith(
systolic: null == systolic ? _self.systolic : systolic // ignore: cast_nullable_to_non_nullable
as int,diastolic: null == diastolic ? _self.diastolic : diastolic // ignore: cast_nullable_to_non_nullable
as int,pulse: freezed == pulse ? _self.pulse : pulse // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}

}


/// @nodoc


class _BloodPressureRecord implements BloodPressureRecord {
  const _BloodPressureRecord({required this.systolic, required this.diastolic, this.pulse});
  

@override final  int systolic;
@override final  int diastolic;
@override final  int? pulse;

/// Create a copy of BloodPressureRecord
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$BloodPressureRecordCopyWith<_BloodPressureRecord> get copyWith => __$BloodPressureRecordCopyWithImpl<_BloodPressureRecord>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _BloodPressureRecord&&(identical(other.systolic, systolic) || other.systolic == systolic)&&(identical(other.diastolic, diastolic) || other.diastolic == diastolic)&&(identical(other.pulse, pulse) || other.pulse == pulse));
}


@override
int get hashCode => Object.hash(runtimeType,systolic,diastolic,pulse);

@override
String toString() {
  return 'BloodPressureRecord(systolic: $systolic, diastolic: $diastolic, pulse: $pulse)';
}


}

/// @nodoc
abstract mixin class _$BloodPressureRecordCopyWith<$Res> implements $BloodPressureRecordCopyWith<$Res> {
  factory _$BloodPressureRecordCopyWith(_BloodPressureRecord value, $Res Function(_BloodPressureRecord) _then) = __$BloodPressureRecordCopyWithImpl;
@override @useResult
$Res call({
 int systolic, int diastolic, int? pulse
});




}
/// @nodoc
class __$BloodPressureRecordCopyWithImpl<$Res>
    implements _$BloodPressureRecordCopyWith<$Res> {
  __$BloodPressureRecordCopyWithImpl(this._self, this._then);

  final _BloodPressureRecord _self;
  final $Res Function(_BloodPressureRecord) _then;

/// Create a copy of BloodPressureRecord
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? systolic = null,Object? diastolic = null,Object? pulse = freezed,}) {
  return _then(_BloodPressureRecord(
systolic: null == systolic ? _self.systolic : systolic // ignore: cast_nullable_to_non_nullable
as int,diastolic: null == diastolic ? _self.diastolic : diastolic // ignore: cast_nullable_to_non_nullable
as int,pulse: freezed == pulse ? _self.pulse : pulse // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}


}

// dart format on
