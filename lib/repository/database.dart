import 'dart:io';

import 'package:drift/drift.dart';
import 'package:drift/native.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as p;

import '../model/blood_pressure.dart';
import '../model/blood_sugar.dart';

part 'database.g.dart';

/// 健康日记数据库
@DriftDatabase(tables: [BloodPressures, BloodSugars])
class HealthDiaryDatabase extends _$HealthDiaryDatabase {
  HealthDiaryDatabase() : super(_openConnection());
  HealthDiaryDatabase.forTesting(super.executor);

  @override
  int get schemaVersion => 1;

  @override
  MigrationStrategy get migration => MigrationStrategy(
    onCreate: (m) async {
      await m.createAll();
    },
  );

  /// 打开数据库连接
  static LazyDatabase _openConnection() {
    return LazyDatabase(() async {
      final dbFolder = await getApplicationDocumentsDirectory();
      final file = File(p.join(dbFolder.path, 'health_diary.sqlite'));
      return NativeDatabase.createInBackground(file);
    });
  }
}
