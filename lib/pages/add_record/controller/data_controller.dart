import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:health_diary/types/health_types.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'data_controller.freezed.dart';
part 'data_controller.g.dart';

@freezed
abstract class BloodSugarRecord with _$BloodSugarRecord {
  const factory BloodSugarRecord({int? dbId, required DateTime time, required double value, String? note}) =
      _BloodSugarRecord;
}

@freezed
abstract class BloodPressureRecord with _$BloodPressureRecord {
  const factory BloodPressureRecord({
    int? dbId,
    required DateTime time,
    required int systolic,
    required int diastolic,
    int? pulse,
    String? note,
  }) = _BloodPressureRecord;
}

@freezed
abstract class DataState with _$DataState {
  const factory DataState({BloodSugarRecord? sugar, BloodPressureRecord? pressure}) = _DataState;
}

@riverpod
class DataController extends _$DataController {

  final scanParser = {
    HealthRecordTypeEnum.bloodPressure: true,
    HealthRecordTypeEnum.bloodSugar: false,
  };

  @override
  DataState build() {
    return DataState();
  }

  void setSugar(BloodSugarRecord sugar) {
    state = state.copyWith(sugar: sugar);
  }

  void updateSugar({
    DateTime? time,
    double? value,
    String? note,
  }) {
    final currentSugar = state.sugar;

    if (currentSugar != null) {
      // 更新现有记录
      state = state.copyWith(
        sugar: currentSugar.copyWith(
          time: time ?? currentSugar.time,
          value: value ?? currentSugar.value,
          note: note ?? currentSugar.note,
        ),
      );
    } else {
      // 创建新记录
      state = state.copyWith(
        sugar: BloodSugarRecord(
          time: time ?? DateTime.now(),
          value: value ?? 0.0,
          note: note,
        ),
      );
    }
  }

  void updatePressure({
    DateTime? time,
    int? systolic,
    int? diastolic,
    int? pulse,
    String? note,
  }) {
    final currentPressure = state.pressure;

    if (currentPressure != null) {
      // 更新现有记录
      state = state.copyWith(
        pressure: currentPressure.copyWith(
          time: time ?? currentPressure.time,
          systolic: systolic ?? currentPressure.systolic,
          diastolic: diastolic ?? currentPressure.diastolic,
          pulse: pulse ?? currentPressure.pulse,
          note: note ?? currentPressure.note,
        ),
      );
    } else {
      // 创建新记录
      state = state.copyWith(
        pressure: BloodPressureRecord(
          time: time ?? DateTime.now(),
          systolic: systolic ?? 0,
          diastolic: diastolic ?? 0,
          pulse: pulse,
          note: note,
        ),
      );
    }
  }

  bool parseBloodPressureScanData(List<int> data) {
    if (data.length < 2) {
      return false;
    }

    List<int> remainingData = List.from(data);
    int? systolic;
    int? diastolic;
    int? pulse;

    // 匹配高压 (收缩压，正常范围约90-200)
    for (int i = 0; i < remainingData.length; i++) {
      int value = remainingData[i];
      if (value >= 50 && value <= 200) {
        systolic = value;
        remainingData.removeAt(i);
        break;
      }
    }

    if (systolic == null) {
      return false;
    }

    // 匹配低压 (舒张压，正常范围约60-120)
    for (int i = 0; i < remainingData.length; i++) {
      int value = remainingData[i];
      if (value >= 30 && value <= 120 && value < systolic) {
        diastolic = value;
        remainingData.removeAt(i);
        break;
      }
    }

    if (diastolic == null) {
      return false;
    }

    // 匹配脉搏 (可选，正常范围约50-150)
    if (remainingData.isNotEmpty) {
      for (int i = 0; i < remainingData.length; i++) {
        int value = remainingData[i];
        if (value >= 40 && value <= 220) {
          pulse = value;
          break;
        }
      }
    }

    final pressure = BloodPressureRecord(
      time: DateTime.now(),
      systolic: systolic,
      diastolic: diastolic,
      pulse: pulse,
    );
    state = state.copyWith(pressure: pressure);

    return true;
  }

  bool parseScanData(List<int> data, HealthRecordTypeEnum recordType) {
    switch (recordType) {
      case HealthRecordTypeEnum.bloodPressure:
        return parseBloodPressureScanData(data);
      case HealthRecordTypeEnum.bloodSugar:
        return false;
    }
  }

  bool canScan(HealthRecordTypeEnum recordType) {
    return scanParser[recordType] ?? false;
  }
}
