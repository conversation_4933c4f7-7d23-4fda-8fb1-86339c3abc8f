// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'health_records_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$healthRecordsRepositoryHash() =>
    r'3de84443d5a91bf951974c6df3e3ef4b21a94cd4';

/// 健康记录仓库提供者
///
/// Copied from [healthRecordsRepository].
@ProviderFor(healthRecordsRepository)
final healthRecordsRepositoryProvider =
    AutoDisposeProvider<HealthRecordsRepository>.internal(
      healthRecordsRepository,
      name: r'healthRecordsRepositoryProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$healthRecordsRepositoryHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef HealthRecordsRepositoryRef =
    AutoDisposeProviderRef<HealthRecordsRepository>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
