// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'blood_pressure_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$bloodPressureRepositoryHash() =>
    r'e453614fe24acc8a65e8229aff44c57216885080';

/// 血压记录仓库提供者
///
/// Copied from [bloodPressureRepository].
@ProviderFor(bloodPressureRepository)
final bloodPressureRepositoryProvider =
    AutoDisposeProvider<BloodPressureRepository>.internal(
      bloodPressureRepository,
      name: r'bloodPressureRepositoryProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$bloodPressureRepositoryHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef BloodPressureRepositoryRef =
    AutoDisposeProviderRef<BloodPressureRepository>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
