import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:health_diary/pages/home/<USER>/recent_records_controller.dart';

void main() {
  group('RecentRecordsController', () {
    test('should initialize controller correctly', () {
      final container = ProviderContainer();
      final controller = container.read(recentRecordsControllerProvider.notifier);

      expect(controller, isNotNull);
    });

    test('should handle refresh correctly', () async {
      final container = ProviderContainer();
      final controller = container.read(recentRecordsControllerProvider.notifier);

      // 测试刷新方法不抛出异常
      expect(() => controller.refresh(), returnsNormally);
    });

    test('should handle loadMore correctly', () async {
      final container = ProviderContainer();
      final controller = container.read(recentRecordsControllerProvider.notifier);

      // 测试加载更多方法不抛出异常
      expect(() => controller.loadMore(), returnsNormally);
    });
  });
}
