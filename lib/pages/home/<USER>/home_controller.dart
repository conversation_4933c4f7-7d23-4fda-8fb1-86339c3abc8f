import 'package:health_diary/types/health_types.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../types/health_record.dart';

part 'home_controller.g.dart';

/// 首页控制器
@riverpod
class HomeController extends _$HomeController {
  @override
  HomeState build() {
    return const HomeState(
      todayOverview: TodayHealthOverview(
        status: '良好',
        bloodPressure: '118/76',
        bloodSugar: '5.2',
        bloodPressureUnit: 'mmHg',
        bloodSugarUnit: 'mmol/L',
      ),
      recentRecords: [
        HealthRecordEntry(
          time: '今天 08:30',
          type: '血压',
          note: '晨起测量，休息5分钟后，坐姿正常',
          recordType: HealthRecordTypeEnum.bloodPressure,
          color: 0xFFFFEBF0,
          values: [
            HealthRecordValue(label: '高压', value: '120', unit: 'mmHg'),
            HealthRecordValue(label: '低压', value: '78', unit: 'mmHg'),
            HealthRecordValue(label: '脉搏', value: '72', unit: 'bpm'),
          ],
        ),
        HealthRecordEntry(
          time: '昨天 18:45',
          type: '血糖',
          note: '晚餐后2小时测量，今天吃了蛋糕',
          recordType: HealthRecordTypeEnum.bloodSugar,
          color: 0xFFE4F4FF,
          values: [HealthRecordValue(label: '血糖', value: '5.4', unit: 'mmol/L')],
        ),
        HealthRecordEntry(
          time: '昨天 08:15',
          type: '血压',
          note: '起床后立即测量，可能偏高',
          recordType: HealthRecordTypeEnum.bloodPressure,
          color: 0xFFFFEBF0,
          values: [
            HealthRecordValue(label: '高压', value: '115', unit: 'mmHg'),
            HealthRecordValue(label: '低压', value: '75', unit: 'mmHg'),
            HealthRecordValue(label: '脉搏', value: '68', unit: 'bpm'),
          ],
        ),
      ],
    );
  }

  /// 刷新数据
  void refresh() {
    // 这里可以重新加载数据
    ref.invalidateSelf();
  }
}

/// 首页状态
class HomeState {
  final TodayHealthOverview todayOverview;
  final List<HealthRecordEntry> recentRecords;

  const HomeState({required this.todayOverview, required this.recentRecords});
}
