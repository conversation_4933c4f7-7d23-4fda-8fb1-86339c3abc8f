import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:health_diary/themes/app_theme.dart';

void main() {
  group('AppTheme Tests', () {
    testWidgets('AppColors should have correct color values', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: Builder(
            builder: (context) {
              final appColors = context.appColors;
              expect(appColors.bloodPressureCard, const Color(0xFFFFEBF0));
              expect(appColors.bloodSugarCard, const Color(0xFFE4F4FF));
              expect(appColors.heartIcon, Colors.pink);
              expect(appColors.dropletIcon, Colors.lightBlue);
              return Container();
            },
          ),
        ),
      );
    });
    
    testWidgets('Theme should provide correct text styles', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: Builder(
            builder: (context) {
              final textTheme = Theme.of(context).textTheme;
              
              // 验证文本样式是否正确设置
              expect(textTheme.headlineLarge?.fontSize, 32);
              expect(textTheme.headlineLarge?.fontWeight, FontWeight.bold);
              expect(textTheme.headlineLarge?.color, const Color(0xFF333333));
              
              expect(textTheme.headlineMedium?.fontSize, 24);
              expect(textTheme.titleLarge?.fontSize, 18);
              expect(textTheme.titleMedium?.fontSize, 14);
              
              return const SizedBox();
            },
          ),
        ),
      );
    });
    
    test('AppColors copyWith should work correctly', () {
      const original = AppColors(
        backgroundGradient: [Color(0xFFE7FFEA), Color(0xFFD4EDDA)],
        bloodPressureCard: Color(0xFFFFEBF0),
        bloodSugarCard: Color(0xFFE4F4FF),
        heartIcon: Colors.pink,
        dropletIcon: Colors.lightBlue,
        bottomNavigationBarIcon: Colors.grey,
        bottomNavigationBarIconSelected: Colors.amber,
      );

      final newColors = original.copyWith(
        heartIcon: Colors.red,
      );

      expect(newColors.heartIcon, Colors.red);
      expect(newColors.bloodPressureCard, const Color(0xFFFFEBF0)); // 应该保持不变
    });
  });
}