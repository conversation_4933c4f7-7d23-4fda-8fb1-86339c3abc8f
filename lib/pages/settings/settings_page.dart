import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

class SettingsPage extends StatelessWidget {
  const SettingsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('settings'.tr()), backgroundColor: Theme.of(context).appBarTheme.backgroundColor),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          Card(
            child: ListTile(
              leading: const FaIcon(FontAwesomeIcons.language),
              title: Text('language'.tr()),
              subtitle: Text(_getCurrentLanguageName(context)),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () => _showLanguageDialog(context),
            ),
          ),
        ],
      ),
    );
  }

  String _getCurrentLanguageName(BuildContext context) {
    final currentLocale = context.locale;
    switch (currentLocale.languageCode) {
      case 'zh':
        return 'chinese'.tr();
      case 'en':
        return 'english'.tr();
      default:
        return currentLocale.languageCode;
    }
  }

  void _showLanguageDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('select_language'.tr()),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                title: Text('chinese'.tr()),
                leading: Radio<String>(
                  value: 'zh',
                  groupValue: context.locale.languageCode,
                  onChanged: (String? value) {
                    if (value != null) {
                      context.setLocale(Locale(value));
                      Navigator.of(context).pop();
                    }
                  },
                ),
                onTap: () {
                  context.setLocale(const Locale('zh'));
                  Navigator.of(context).pop();
                },
              ),
              ListTile(
                title: Text('english'.tr()),
                leading: Radio<String>(
                  value: 'en',
                  groupValue: context.locale.languageCode,
                  onChanged: (String? value) {
                    if (value != null) {
                      context.setLocale(Locale(value));
                      Navigator.of(context).pop();
                    }
                  },
                ),
                onTap: () {
                  context.setLocale(const Locale('en'));
                  Navigator.of(context).pop();
                },
              ),
            ],
          ),
          actions: [TextButton(onPressed: () => Navigator.of(context).pop(), child: Text('cancel'.tr()))],
        );
      },
    );
  }
}
