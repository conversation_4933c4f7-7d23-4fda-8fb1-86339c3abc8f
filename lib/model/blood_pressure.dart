import 'package:drift/drift.dart';

/// 血压记录数据库表定义
class BloodPressures extends Table {
  IntColumn get id => integer().autoIncrement()();
  IntColumn get systolic => integer()(); // 高压（收缩压）
  IntColumn get diastolic => integer()(); // 低压（舒张压）
  IntColumn get pulse => integer().nullable()(); // 脉搏（可选）
  TextColumn get note => text().nullable()(); // 备注
  DateTimeColumn get createdAt => dateTime()(); // 创建时间
}
