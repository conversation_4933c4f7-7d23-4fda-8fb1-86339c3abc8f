import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:drift/drift.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../providers/database_provider.dart';
import 'database.dart';

part 'blood_sugar_repository.g.dart';

/// 血糖记录仓库
class BloodSugarRepository {
  final HealthDiaryDatabase _database;

  BloodSugarRepository(this._database);

  /// 获取所有血糖记录
  Stream<List<BloodSugar>> getAllBloodSugars() {
    return (_database.select(_database.bloodSugars)..orderBy([(t) => OrderingTerm.desc(t.createdAt)])).watch();
  }

  /// 根据ID获取血糖记录
  Future<BloodSugar?> getBloodSugarById(int id) {
    return (_database.select(_database.bloodSugars)..where((t) => t.id.equals(id))).getSingleOrNull();
  }

  /// 添加血糖记录
  Future<int> addBloodSugar({
    required double value,
    String? note,
    required DateTime createdAt,
  }) {
    final companion = BloodSugarsCompanion(
      value: Value(value),
      note: note != null ? Value(note) : const Value.absent(),
      createdAt: Value(createdAt),
    );
    return _database.into(_database.bloodSugars).insert(companion);
  }

  /// 更新血糖记录
  Future<bool> updateBloodSugar(BloodSugar entry) {
    return _database.update(_database.bloodSugars).replace(entry);
  }

  /// 删除血糖记录
  Future<int> deleteBloodSugar(int id) {
    return (_database.delete(_database.bloodSugars)..where((t) => t.id.equals(id))).go();
  }

  /// 批量删除血糖记录
  Future<int> deleteBloodSugars(List<int> ids) {
    return (_database.delete(_database.bloodSugars)..where((t) => t.id.isIn(ids))).go();
  }

  /// 根据日期范围获取血糖记录
  Stream<List<BloodSugar>> getBloodSugarsByDateRange(DateTime startDate, DateTime endDate) {
    return (_database.select(_database.bloodSugars)
          ..where((t) => t.createdAt.isBetweenValues(startDate, endDate))
          ..orderBy([(t) => OrderingTerm.desc(t.createdAt)]))
        .watch();
  }

  /// 获取最近的血糖记录
  Future<BloodSugar?> getLatestBloodSugar() {
    return (_database.select(_database.bloodSugars)
          ..orderBy([(t) => OrderingTerm.desc(t.createdAt)])
          ..limit(1))
        .getSingleOrNull();
  }
}

/// 血糖记录仓库提供者
@riverpod
BloodSugarRepository bloodSugarRepository(Ref ref) {
  final database = ref.watch(databaseProvider);
  return BloodSugarRepository(database);
}