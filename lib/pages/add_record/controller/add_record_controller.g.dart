// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'add_record_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$addRecordControllerHash() =>
    r'f2b8d77762aed48e7a2e5e57e654c3110393de2b';

/// See also [AddRecordController].
@ProviderFor(AddRecordController)
final addRecordControllerProvider =
    AutoDisposeNotifierProvider<AddRecordController, AddRecordState>.internal(
      AddRecordController.new,
      name: r'addRecordControllerProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$addRecordControllerHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$AddRecordController = AutoDisposeNotifier<AddRecordState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
