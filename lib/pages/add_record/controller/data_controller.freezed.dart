// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'data_controller.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$BloodSugarRecord {

 int? get dbId; DateTime get time; double get value; String? get note;
/// Create a copy of BloodSugarRecord
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$BloodSugarRecordCopyWith<BloodSugarRecord> get copyWith => _$BloodSugarRecordCopyWithImpl<BloodSugarRecord>(this as BloodSugarRecord, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is BloodSugarRecord&&(identical(other.dbId, dbId) || other.dbId == dbId)&&(identical(other.time, time) || other.time == time)&&(identical(other.value, value) || other.value == value)&&(identical(other.note, note) || other.note == note));
}


@override
int get hashCode => Object.hash(runtimeType,dbId,time,value,note);

@override
String toString() {
  return 'BloodSugarRecord(dbId: $dbId, time: $time, value: $value, note: $note)';
}


}

/// @nodoc
abstract mixin class $BloodSugarRecordCopyWith<$Res>  {
  factory $BloodSugarRecordCopyWith(BloodSugarRecord value, $Res Function(BloodSugarRecord) _then) = _$BloodSugarRecordCopyWithImpl;
@useResult
$Res call({
 int? dbId, DateTime time, double value, String? note
});




}
/// @nodoc
class _$BloodSugarRecordCopyWithImpl<$Res>
    implements $BloodSugarRecordCopyWith<$Res> {
  _$BloodSugarRecordCopyWithImpl(this._self, this._then);

  final BloodSugarRecord _self;
  final $Res Function(BloodSugarRecord) _then;

/// Create a copy of BloodSugarRecord
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? dbId = freezed,Object? time = null,Object? value = null,Object? note = freezed,}) {
  return _then(_self.copyWith(
dbId: freezed == dbId ? _self.dbId : dbId // ignore: cast_nullable_to_non_nullable
as int?,time: null == time ? _self.time : time // ignore: cast_nullable_to_non_nullable
as DateTime,value: null == value ? _self.value : value // ignore: cast_nullable_to_non_nullable
as double,note: freezed == note ? _self.note : note // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// @nodoc


class _BloodSugarRecord implements BloodSugarRecord {
  const _BloodSugarRecord({this.dbId, required this.time, required this.value, this.note});
  

@override final  int? dbId;
@override final  DateTime time;
@override final  double value;
@override final  String? note;

/// Create a copy of BloodSugarRecord
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$BloodSugarRecordCopyWith<_BloodSugarRecord> get copyWith => __$BloodSugarRecordCopyWithImpl<_BloodSugarRecord>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _BloodSugarRecord&&(identical(other.dbId, dbId) || other.dbId == dbId)&&(identical(other.time, time) || other.time == time)&&(identical(other.value, value) || other.value == value)&&(identical(other.note, note) || other.note == note));
}


@override
int get hashCode => Object.hash(runtimeType,dbId,time,value,note);

@override
String toString() {
  return 'BloodSugarRecord(dbId: $dbId, time: $time, value: $value, note: $note)';
}


}

/// @nodoc
abstract mixin class _$BloodSugarRecordCopyWith<$Res> implements $BloodSugarRecordCopyWith<$Res> {
  factory _$BloodSugarRecordCopyWith(_BloodSugarRecord value, $Res Function(_BloodSugarRecord) _then) = __$BloodSugarRecordCopyWithImpl;
@override @useResult
$Res call({
 int? dbId, DateTime time, double value, String? note
});




}
/// @nodoc
class __$BloodSugarRecordCopyWithImpl<$Res>
    implements _$BloodSugarRecordCopyWith<$Res> {
  __$BloodSugarRecordCopyWithImpl(this._self, this._then);

  final _BloodSugarRecord _self;
  final $Res Function(_BloodSugarRecord) _then;

/// Create a copy of BloodSugarRecord
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? dbId = freezed,Object? time = null,Object? value = null,Object? note = freezed,}) {
  return _then(_BloodSugarRecord(
dbId: freezed == dbId ? _self.dbId : dbId // ignore: cast_nullable_to_non_nullable
as int?,time: null == time ? _self.time : time // ignore: cast_nullable_to_non_nullable
as DateTime,value: null == value ? _self.value : value // ignore: cast_nullable_to_non_nullable
as double,note: freezed == note ? _self.note : note // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

/// @nodoc
mixin _$BloodPressureRecord {

 int? get dbId; DateTime get time; int get systolic; int get diastolic; int? get pulse; String? get note;
/// Create a copy of BloodPressureRecord
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$BloodPressureRecordCopyWith<BloodPressureRecord> get copyWith => _$BloodPressureRecordCopyWithImpl<BloodPressureRecord>(this as BloodPressureRecord, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is BloodPressureRecord&&(identical(other.dbId, dbId) || other.dbId == dbId)&&(identical(other.time, time) || other.time == time)&&(identical(other.systolic, systolic) || other.systolic == systolic)&&(identical(other.diastolic, diastolic) || other.diastolic == diastolic)&&(identical(other.pulse, pulse) || other.pulse == pulse)&&(identical(other.note, note) || other.note == note));
}


@override
int get hashCode => Object.hash(runtimeType,dbId,time,systolic,diastolic,pulse,note);

@override
String toString() {
  return 'BloodPressureRecord(dbId: $dbId, time: $time, systolic: $systolic, diastolic: $diastolic, pulse: $pulse, note: $note)';
}


}

/// @nodoc
abstract mixin class $BloodPressureRecordCopyWith<$Res>  {
  factory $BloodPressureRecordCopyWith(BloodPressureRecord value, $Res Function(BloodPressureRecord) _then) = _$BloodPressureRecordCopyWithImpl;
@useResult
$Res call({
 int? dbId, DateTime time, int systolic, int diastolic, int? pulse, String? note
});




}
/// @nodoc
class _$BloodPressureRecordCopyWithImpl<$Res>
    implements $BloodPressureRecordCopyWith<$Res> {
  _$BloodPressureRecordCopyWithImpl(this._self, this._then);

  final BloodPressureRecord _self;
  final $Res Function(BloodPressureRecord) _then;

/// Create a copy of BloodPressureRecord
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? dbId = freezed,Object? time = null,Object? systolic = null,Object? diastolic = null,Object? pulse = freezed,Object? note = freezed,}) {
  return _then(_self.copyWith(
dbId: freezed == dbId ? _self.dbId : dbId // ignore: cast_nullable_to_non_nullable
as int?,time: null == time ? _self.time : time // ignore: cast_nullable_to_non_nullable
as DateTime,systolic: null == systolic ? _self.systolic : systolic // ignore: cast_nullable_to_non_nullable
as int,diastolic: null == diastolic ? _self.diastolic : diastolic // ignore: cast_nullable_to_non_nullable
as int,pulse: freezed == pulse ? _self.pulse : pulse // ignore: cast_nullable_to_non_nullable
as int?,note: freezed == note ? _self.note : note // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// @nodoc


class _BloodPressureRecord implements BloodPressureRecord {
  const _BloodPressureRecord({this.dbId, required this.time, required this.systolic, required this.diastolic, this.pulse, this.note});
  

@override final  int? dbId;
@override final  DateTime time;
@override final  int systolic;
@override final  int diastolic;
@override final  int? pulse;
@override final  String? note;

/// Create a copy of BloodPressureRecord
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$BloodPressureRecordCopyWith<_BloodPressureRecord> get copyWith => __$BloodPressureRecordCopyWithImpl<_BloodPressureRecord>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _BloodPressureRecord&&(identical(other.dbId, dbId) || other.dbId == dbId)&&(identical(other.time, time) || other.time == time)&&(identical(other.systolic, systolic) || other.systolic == systolic)&&(identical(other.diastolic, diastolic) || other.diastolic == diastolic)&&(identical(other.pulse, pulse) || other.pulse == pulse)&&(identical(other.note, note) || other.note == note));
}


@override
int get hashCode => Object.hash(runtimeType,dbId,time,systolic,diastolic,pulse,note);

@override
String toString() {
  return 'BloodPressureRecord(dbId: $dbId, time: $time, systolic: $systolic, diastolic: $diastolic, pulse: $pulse, note: $note)';
}


}

/// @nodoc
abstract mixin class _$BloodPressureRecordCopyWith<$Res> implements $BloodPressureRecordCopyWith<$Res> {
  factory _$BloodPressureRecordCopyWith(_BloodPressureRecord value, $Res Function(_BloodPressureRecord) _then) = __$BloodPressureRecordCopyWithImpl;
@override @useResult
$Res call({
 int? dbId, DateTime time, int systolic, int diastolic, int? pulse, String? note
});




}
/// @nodoc
class __$BloodPressureRecordCopyWithImpl<$Res>
    implements _$BloodPressureRecordCopyWith<$Res> {
  __$BloodPressureRecordCopyWithImpl(this._self, this._then);

  final _BloodPressureRecord _self;
  final $Res Function(_BloodPressureRecord) _then;

/// Create a copy of BloodPressureRecord
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? dbId = freezed,Object? time = null,Object? systolic = null,Object? diastolic = null,Object? pulse = freezed,Object? note = freezed,}) {
  return _then(_BloodPressureRecord(
dbId: freezed == dbId ? _self.dbId : dbId // ignore: cast_nullable_to_non_nullable
as int?,time: null == time ? _self.time : time // ignore: cast_nullable_to_non_nullable
as DateTime,systolic: null == systolic ? _self.systolic : systolic // ignore: cast_nullable_to_non_nullable
as int,diastolic: null == diastolic ? _self.diastolic : diastolic // ignore: cast_nullable_to_non_nullable
as int,pulse: freezed == pulse ? _self.pulse : pulse // ignore: cast_nullable_to_non_nullable
as int?,note: freezed == note ? _self.note : note // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

/// @nodoc
mixin _$DataState {

 BloodSugarRecord? get sugar; BloodPressureRecord? get pressure;
/// Create a copy of DataState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$DataStateCopyWith<DataState> get copyWith => _$DataStateCopyWithImpl<DataState>(this as DataState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is DataState&&(identical(other.sugar, sugar) || other.sugar == sugar)&&(identical(other.pressure, pressure) || other.pressure == pressure));
}


@override
int get hashCode => Object.hash(runtimeType,sugar,pressure);

@override
String toString() {
  return 'DataState(sugar: $sugar, pressure: $pressure)';
}


}

/// @nodoc
abstract mixin class $DataStateCopyWith<$Res>  {
  factory $DataStateCopyWith(DataState value, $Res Function(DataState) _then) = _$DataStateCopyWithImpl;
@useResult
$Res call({
 BloodSugarRecord? sugar, BloodPressureRecord? pressure
});


$BloodSugarRecordCopyWith<$Res>? get sugar;$BloodPressureRecordCopyWith<$Res>? get pressure;

}
/// @nodoc
class _$DataStateCopyWithImpl<$Res>
    implements $DataStateCopyWith<$Res> {
  _$DataStateCopyWithImpl(this._self, this._then);

  final DataState _self;
  final $Res Function(DataState) _then;

/// Create a copy of DataState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? sugar = freezed,Object? pressure = freezed,}) {
  return _then(_self.copyWith(
sugar: freezed == sugar ? _self.sugar : sugar // ignore: cast_nullable_to_non_nullable
as BloodSugarRecord?,pressure: freezed == pressure ? _self.pressure : pressure // ignore: cast_nullable_to_non_nullable
as BloodPressureRecord?,
  ));
}
/// Create a copy of DataState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$BloodSugarRecordCopyWith<$Res>? get sugar {
    if (_self.sugar == null) {
    return null;
  }

  return $BloodSugarRecordCopyWith<$Res>(_self.sugar!, (value) {
    return _then(_self.copyWith(sugar: value));
  });
}/// Create a copy of DataState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$BloodPressureRecordCopyWith<$Res>? get pressure {
    if (_self.pressure == null) {
    return null;
  }

  return $BloodPressureRecordCopyWith<$Res>(_self.pressure!, (value) {
    return _then(_self.copyWith(pressure: value));
  });
}
}


/// @nodoc


class _DataState implements DataState {
  const _DataState({this.sugar, this.pressure});
  

@override final  BloodSugarRecord? sugar;
@override final  BloodPressureRecord? pressure;

/// Create a copy of DataState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$DataStateCopyWith<_DataState> get copyWith => __$DataStateCopyWithImpl<_DataState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _DataState&&(identical(other.sugar, sugar) || other.sugar == sugar)&&(identical(other.pressure, pressure) || other.pressure == pressure));
}


@override
int get hashCode => Object.hash(runtimeType,sugar,pressure);

@override
String toString() {
  return 'DataState(sugar: $sugar, pressure: $pressure)';
}


}

/// @nodoc
abstract mixin class _$DataStateCopyWith<$Res> implements $DataStateCopyWith<$Res> {
  factory _$DataStateCopyWith(_DataState value, $Res Function(_DataState) _then) = __$DataStateCopyWithImpl;
@override @useResult
$Res call({
 BloodSugarRecord? sugar, BloodPressureRecord? pressure
});


@override $BloodSugarRecordCopyWith<$Res>? get sugar;@override $BloodPressureRecordCopyWith<$Res>? get pressure;

}
/// @nodoc
class __$DataStateCopyWithImpl<$Res>
    implements _$DataStateCopyWith<$Res> {
  __$DataStateCopyWithImpl(this._self, this._then);

  final _DataState _self;
  final $Res Function(_DataState) _then;

/// Create a copy of DataState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? sugar = freezed,Object? pressure = freezed,}) {
  return _then(_DataState(
sugar: freezed == sugar ? _self.sugar : sugar // ignore: cast_nullable_to_non_nullable
as BloodSugarRecord?,pressure: freezed == pressure ? _self.pressure : pressure // ignore: cast_nullable_to_non_nullable
as BloodPressureRecord?,
  ));
}

/// Create a copy of DataState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$BloodSugarRecordCopyWith<$Res>? get sugar {
    if (_self.sugar == null) {
    return null;
  }

  return $BloodSugarRecordCopyWith<$Res>(_self.sugar!, (value) {
    return _then(_self.copyWith(sugar: value));
  });
}/// Create a copy of DataState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$BloodPressureRecordCopyWith<$Res>? get pressure {
    if (_self.pressure == null) {
    return null;
  }

  return $BloodPressureRecordCopyWith<$Res>(_self.pressure!, (value) {
    return _then(_self.copyWith(pressure: value));
  });
}
}

// dart format on
