part of 'scan_provider.dart';

/// 图像预处理器
class ImagePreprocessor {
  final ModelInfo _modelInfo;
  
  const ImagePreprocessor(this._modelInfo);
  
  /// 预处理图像数据
  Future<List<List<List<List<double>>>>> preprocessImage(img.Image image) async {
    try {
      img.Image resized = image;
      if (image.width != _modelInfo.inputWidth || 
          image.height != _modelInfo.inputHeight) {
        debugPrint('Resizing image from ${image.width}x${image.height} to ${_modelInfo.inputWidth}x${_modelInfo.inputHeight}');
        resized = img.copyResize(
          image, 
          width: _modelInfo.inputWidth, 
          height: _modelInfo.inputHeight,
          interpolation: img.Interpolation.average,
        );
      }
      
      // if (kDebugMode || kProfileMode) {
      //   final pngBytes = img.encodePng(resized);
      //   final path = await DebugHelper.saveDebugData(
      //     pngBytes, 
      //     'resized_image_${DateTime.now().millisecondsSinceEpoch}.png'
      //   );

      //   debugPrint('Resized image saved to: $path');
      // }
      
      return _convertImageToInputBuffer(resized);
    } catch (e, stackTrace) {
      debugPrint('Error in image preprocessingL: $e, $stackTrace');
      rethrow;
    }
  }
  
  List<List<List<List<double>>>> _convertImageToInputBuffer(img.Image image) {
    final inputBuffer = [
      List.generate(
        _modelInfo.inputHeight,
        (y) => List.generate(
          _modelInfo.inputWidth,
          (x) {
            final pixel = image.getPixel(x, y);
            final r = pixel.rNormalized.toDouble();
            final g = pixel.gNormalized.toDouble();
            final b = pixel.bNormalized.toDouble();
            return [r, g, b];
          },
        ),
      ),
    ];
    
    return inputBuffer;
  }
}