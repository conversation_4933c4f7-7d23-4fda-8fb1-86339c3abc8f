import 'package:freezed_annotation/freezed_annotation.dart';

part 'health_types.freezed.dart';

@freezed
abstract class BloodSugarRecord with _$BloodSugarRecord {
  const factory BloodSugarRecord({required double value}) = _BloodSugarRecord;
}

@freezed
abstract class BloodPressureRecord with _$BloodPressureRecord {
  const factory BloodPressureRecord({required int systolic, required int diastolic, int? pulse}) = _BloodPressureRecord;
}

/// 健康记录类型
enum HealthRecordTypeEnum {
  bloodPressure,
  bloodSugar;

  static HealthRecordTypeEnum fromInt(int i) {
    return HealthRecordTypeEnum.values[i];
  }

  int toInt() {
    return index;
  }
}
