import 'dart:developer';

import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../repository/health_records_repository.dart';
import '../../../types/health_record.dart';

part 'recent_records_controller.g.dart';

/// 最近记录控制器
@riverpod
class RecentRecordsController extends _$RecentRecordsController {
  static const int pageSize = 100;
  int _currentPage = 1;
  bool _isLoading = false;
  bool _hasMore = true;

  @override
  Future<List<HealthRecordEntry>> build() {
    return _loadRecords(page: 1);
  }

  /// 加载记录
  Future<List<HealthRecordEntry>> _loadRecords({required int page}) async {
    try {
      _isLoading = true;
      final repository = ref.read(healthRecordsRepositoryProvider);
      final records = await repository.getHealthRecordsPaginated(
        page: page,
        pageSize: pageSize,
      );
      _currentPage = page;

      // 如果返回的记录数少于 pageSize，说明没有更多数据了
      if (records.length < pageSize) {
        _hasMore = false;
      }

      return records;
    } catch (e) {
      log('Error loading records: $e');
      rethrow;
    } finally {
      _isLoading = false;
    }
  }

  /// 加载更多数据
  Future<void> loadMore() async {
    if (_isLoading) {
      return;
    }

    try {
      _isLoading = true;
      final nextPage = _currentPage + 1;
      final newRecords = await _loadRecords(page: nextPage);

      // 获取当前状态的记录
      final currentState = ref.read(recentRecordsControllerProvider);
      final currentRecords = currentState.value;
      if (currentRecords != null) {
        // 合并新记录到当前记录
        final allRecords = [...currentRecords, ...newRecords];
        state = AsyncValue.data(allRecords);
      }
    } catch (e) {
      log('Error loading more data: $e');
      // 保持当前状态不变
    } finally {
      _isLoading = false;
    }
  }

  /// 刷新数据
  Future<void> refresh() async {
    try {
      _currentPage = 1;
      _hasMore = true; // 重置 hasMore 状态
      state = AsyncValue.data(await _loadRecords(page: 1));
    } catch (e) {
      log('Error refreshing data: $e');
      state = AsyncValue.error(e, StackTrace.current);
    }
  }

  bool get hasMore => _hasMore;
  bool get isLoading => _isLoading;
}
