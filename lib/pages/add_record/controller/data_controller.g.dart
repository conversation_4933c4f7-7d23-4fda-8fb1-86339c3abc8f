// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'data_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$dataControllerHash() => r'5936c9f2d2329e20761a281238b7454ba61f2590';

/// See also [DataController].
@ProviderFor(DataController)
final dataControllerProvider =
    AutoDisposeNotifierProvider<DataController, DataState>.internal(
      DataController.new,
      name: r'dataControllerProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$dataControllerHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$DataController = AutoDisposeNotifier<DataState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
