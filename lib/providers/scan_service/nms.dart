part of 'scan_provider.dart';


/// Represents a single object detection result.
class Detection {
  final Rect boundingBox; // The bounding box (x, y, width, height or x_min, y_min, x_max, y_max)
  final double confidence; // Confidence score of the detection
  final int classId; // Class ID of the detected object
  final String label; // Optional: The human-readable label for the class ID

  Detection({
    required this.boundingBox,
    required this.confidence,
    required this.classId,
    this.label = '', // Default to empty string if no label is provided
  });

  @override
  String toString() {
    return 'Detection(label: $label, confidence: ${confidence.toStringAsFixed(2)}, '
        'box: [${boundingBox.left.toStringAsFixed(2)}, ${boundingBox.top.toStringAsFixed(2)}, '
        '${boundingBox.right.toStringAsFixed(2)}, ${boundingBox.bottom.toStringAsFixed(2)}])';
  }
}

/// Calculates the Intersection over Union (IoU) of two bounding boxes.
/// Boxes are expected in [x_min, y_min, x_max, y_max] format.
///
/// Returns a value between 0.0 and 1.0.
double _calculateIoU(Rect boxA, Rect boxB) {
  // Determine the coordinates of the intersection rectangle
  final double xA = max(boxA.left, boxB.left);
  final double yA = max(boxA.top, boxB.top);
  final double xB = min(boxA.right, boxB.right);
  final double yB = min(boxA.bottom, boxB.bottom);

  // If there is no intersection (e.g., width or height of intersection is non-positive), return 0.0
  if (xB <= xA || yB <= yA) {
    return 0.0;
  }

  // Compute the area of intersection rectangle
  final double interArea = (xB - xA) * (yB - yA);

  // Compute the area of both the prediction and ground-truth rectangles
  final double boxAArea = boxA.width * boxA.height;
  final double boxBArea = boxB.width * boxB.height;

  // Compute the union area: sum of individual areas minus the intersection area
  final double unionArea = boxAArea + boxBArea - interArea;

  // Handle case where unionArea might be zero.
  if (unionArea <= 0) {
    return 0.0;
  }

  // Compute the Intersection over Union
  return interArea / unionArea;
}

/// Performs Non-Maximum Suppression (NMS) on a list of raw object detection outputs using a per-class approach.
///
/// [rawOutput]: A list of raw detections from the model output.
///   Each inner list is expected to be `[x_min, y_min, x_max, y_max, confidence, class_id]`.
///   Coordinates are expected to be normalized (e.g., within [0, 1]).
/// [iouThreshold]: The IoU threshold for suppressing overlapping boxes (e.g., 0.45).
/// [confidenceThreshold]: The confidence threshold for initial filtering (e.g., 0.25).
/// [classLabels]: Optional list of class names to map class IDs to human-readable labels.
///
/// Returns a list of filtered and suppressed `Detection` objects.
List<Detection> _nms(
  List<List<double>> rawOutput,
  double iouThreshold,
  double confidenceThreshold, {
  List<String>? classLabels,
}) {
  // Step 1: Filter by confidence and group detections by class ID in a single pass.
  final Map<int, List<Detection>> detectionsByClass = {};

  for (final List<double> values in rawOutput) {
    if (values.length < 6) continue; // Skip malformed entries

    final double confidence = values[4];
    if (confidence < confidenceThreshold) continue; // Skip low-confidence detections

    final int classId = values[5].toInt();
    final detection = Detection(
      boundingBox: Rect.fromLTRB(values[0], values[1], values[2], values[3]),
      confidence: confidence,
      classId: classId,
      label: classLabels != null && classId >= 0 && classId < classLabels.length
          ? classLabels[classId]
          : classId.toString(),
    );
    
    // Add the detection to the list for its class. If the list doesn't exist, create it.
    detectionsByClass.putIfAbsent(classId, () => []).add(detection);
  }

  // If no detections passed the confidence threshold, return early.
  if (detectionsByClass.isEmpty) {
    return [];
  }

  final List<Detection> finalDetections = [];

  // Step 2: Apply NMS independently to each class.
  for (final List<Detection> classDetections in detectionsByClass.values) {
    // Sort detections within this class by confidence in descending order.
    classDetections.sort((a, b) => b.confidence.compareTo(a.confidence));

    final List<Detection> keepers = [];
    final List<bool> suppressed = List<bool>.filled(classDetections.length, false);

    for (int i = 0; i < classDetections.length; i++) {
      if (suppressed[i]) continue;

      keepers.add(classDetections[i]);

      for (int j = i + 1; j < classDetections.length; j++) {
        if (suppressed[j]) continue;

        final double iou = _calculateIoU(
          classDetections[i].boundingBox,
          classDetections[j].boundingBox,
        );

        if (iou > iouThreshold) {
          suppressed[j] = true;
        }
      }
    }
    // Add the keepers from this class to the final results.
    finalDetections.addAll(keepers);
  }

  return finalDetections;
}
