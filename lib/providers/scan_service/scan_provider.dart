import 'dart:isolate';
import 'dart:typed_data';

import 'package:tflite_flutter/tflite_flutter.dart';
import 'dart:io';
import 'dart:math';
import 'dart:ui';
import 'package:image/image.dart' as img;
import 'package:flutter/foundation.dart';

part 'nms.dart';
part 'digit_grouper.dart';
part 'image_preprocessor.dart';
part 'model_info.dart';
part 'debug_helper.dart';

/// 扫描服务主类
class ScanService {
  final Interpreter _interpreter;
  final ImagePreprocessor _preprocessor;
  final DigitGrouper _digitGrouper;

  ScanService._(this._interpreter,  ModelInfo modelInfo)
    : _preprocessor = ImagePreprocessor(modelInfo),
      _digitGrouper = DigitGrouper();

  /// 工厂构造函数
  factory ScanService(Interpreter interpreter) {
    final modelInfo = ModelInfo.fromInterpreter(interpreter);
    return ScanService._(interpreter,  modelInfo);
  }

  /// 创建输出缓冲区
  List<List<List<double>>> _createOutputBuffer() {
    final outputTensor = _interpreter.getOutputTensors()[0];
    return List<List<List<double>>>.filled(
      outputTensor.shape[0],
      List<List<double>>.filled(outputTensor.shape[1], List<double>.filled(outputTensor.shape[2], 0.0)),
    );
  }

  static Future<TransferableTypedData?> _scanOnIsolate(TransferableTypedData imageData, int address) async {
    try {
      // 解码图片
      final Uint8List list = imageData.materialize().asUint8List();
      final img.Image? image = img.decodeImage(list);
      if (image == null) {
        return null;
      }

      final interpreter = Interpreter.fromAddress(address);

      final scanService = ScanService(interpreter);

      // 预处理图片
      final inputBuffer = await scanService._preprocessor.preprocessImage(image);

      // 创建输出缓冲区
      final output = scanService._createOutputBuffer();

      // 运行推理
      interpreter.run(inputBuffer, output);

      // 应用NMS算法
      final nmsResults = _nms(output[0], 0.45, 0.25);
      debugPrint('NMS results: $nmsResults');

      // 分组和排序数字
      final group = scanService._digitGrouper.groupAndSort(nmsResults);
      return TransferableTypedData.fromList([Uint32List.fromList(group)]);
    } catch (e, s) {
      debugPrint('Error during image recognition: $e. $s');
      return null;
    }
  }

  static Future<List<int>> scan({required Uint8List imageData}) async {
    final transferableData = TransferableTypedData.fromList([imageData]);

        final options = InterpreterOptions()
      ..useMetalDelegateForIOS = true
      ..threads = 2;
    
      final interpreter = await Interpreter.fromAsset(
        // 'assets/models/health_data_model.tflite', 
        // 'assets/models/best_float32.tflite', 
        'assets/models/best_float16.tflite', 
        options: options
      );

    final ret = await Isolate.run(() => _scanOnIsolate(transferableData, interpreter.address));
    if (ret == null) {
      return List.empty();
    }

    final list = ret.materialize().asUint32List();
    return list;
  }
}