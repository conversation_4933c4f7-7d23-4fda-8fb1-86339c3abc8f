// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'add_record_controller.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$AddRecordState {

 AddRecordInputType get inputType; HealthRecordTypeEnum get recordType; bool get hasScanError;
/// Create a copy of AddRecordState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AddRecordStateCopyWith<AddRecordState> get copyWith => _$AddRecordStateCopyWithImpl<AddRecordState>(this as AddRecordState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AddRecordState&&(identical(other.inputType, inputType) || other.inputType == inputType)&&(identical(other.recordType, recordType) || other.recordType == recordType)&&(identical(other.hasScanError, hasScanError) || other.hasScanError == hasScanError));
}


@override
int get hashCode => Object.hash(runtimeType,inputType,recordType,hasScanError);

@override
String toString() {
  return 'AddRecordState(inputType: $inputType, recordType: $recordType, hasScanError: $hasScanError)';
}


}

/// @nodoc
abstract mixin class $AddRecordStateCopyWith<$Res>  {
  factory $AddRecordStateCopyWith(AddRecordState value, $Res Function(AddRecordState) _then) = _$AddRecordStateCopyWithImpl;
@useResult
$Res call({
 AddRecordInputType inputType, HealthRecordTypeEnum recordType, bool hasScanError
});




}
/// @nodoc
class _$AddRecordStateCopyWithImpl<$Res>
    implements $AddRecordStateCopyWith<$Res> {
  _$AddRecordStateCopyWithImpl(this._self, this._then);

  final AddRecordState _self;
  final $Res Function(AddRecordState) _then;

/// Create a copy of AddRecordState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? inputType = null,Object? recordType = null,Object? hasScanError = null,}) {
  return _then(_self.copyWith(
inputType: null == inputType ? _self.inputType : inputType // ignore: cast_nullable_to_non_nullable
as AddRecordInputType,recordType: null == recordType ? _self.recordType : recordType // ignore: cast_nullable_to_non_nullable
as HealthRecordTypeEnum,hasScanError: null == hasScanError ? _self.hasScanError : hasScanError // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// @nodoc


class _AddRecordState implements AddRecordState {
   _AddRecordState({required this.inputType, required this.recordType, this.hasScanError = false});
  

@override final  AddRecordInputType inputType;
@override final  HealthRecordTypeEnum recordType;
@override@JsonKey() final  bool hasScanError;

/// Create a copy of AddRecordState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$AddRecordStateCopyWith<_AddRecordState> get copyWith => __$AddRecordStateCopyWithImpl<_AddRecordState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _AddRecordState&&(identical(other.inputType, inputType) || other.inputType == inputType)&&(identical(other.recordType, recordType) || other.recordType == recordType)&&(identical(other.hasScanError, hasScanError) || other.hasScanError == hasScanError));
}


@override
int get hashCode => Object.hash(runtimeType,inputType,recordType,hasScanError);

@override
String toString() {
  return 'AddRecordState(inputType: $inputType, recordType: $recordType, hasScanError: $hasScanError)';
}


}

/// @nodoc
abstract mixin class _$AddRecordStateCopyWith<$Res> implements $AddRecordStateCopyWith<$Res> {
  factory _$AddRecordStateCopyWith(_AddRecordState value, $Res Function(_AddRecordState) _then) = __$AddRecordStateCopyWithImpl;
@override @useResult
$Res call({
 AddRecordInputType inputType, HealthRecordTypeEnum recordType, bool hasScanError
});




}
/// @nodoc
class __$AddRecordStateCopyWithImpl<$Res>
    implements _$AddRecordStateCopyWith<$Res> {
  __$AddRecordStateCopyWithImpl(this._self, this._then);

  final _AddRecordState _self;
  final $Res Function(_AddRecordState) _then;

/// Create a copy of AddRecordState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? inputType = null,Object? recordType = null,Object? hasScanError = null,}) {
  return _then(_AddRecordState(
inputType: null == inputType ? _self.inputType : inputType // ignore: cast_nullable_to_non_nullable
as AddRecordInputType,recordType: null == recordType ? _self.recordType : recordType // ignore: cast_nullable_to_non_nullable
as HealthRecordTypeEnum,hasScanError: null == hasScanError ? _self.hasScanError : hasScanError // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

// dart format on
