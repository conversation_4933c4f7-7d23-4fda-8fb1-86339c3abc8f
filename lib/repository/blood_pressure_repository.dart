import 'package:drift/drift.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../providers/database_provider.dart';
import 'database.dart';

part 'blood_pressure_repository.g.dart';

/// 血压记录仓库
class BloodPressureRepository {
  final HealthDiaryDatabase _database;

  BloodPressureRepository(this._database);

  /// 获取所有血压记录
  Stream<List<BloodPressure>> getAllBloodPressures() {
    return (_database.select(_database.bloodPressures)..orderBy([(t) => OrderingTerm.desc(t.createdAt)])).watch();
  }

  /// 根据ID获取血压记录
  Future<BloodPressure?> getBloodPressureById(int id) {
    return (_database.select(_database.bloodPressures)..where((t) => t.id.equals(id))).getSingleOrNull();
  }

  /// 添加血压记录
  Future<int> addBloodPressure({
    required int systolic,
    required int diastolic,
    int? pulse,
    String? note,
    required DateTime createdAt,
  }) {
    final companion = BloodPressuresCompanion(
      systolic: Value(systolic),
      diastolic: Value(diastolic),
      pulse: pulse != null ? Value(pulse) : const Value.absent(),
      note: note != null ? Value(note) : const Value.absent(),
      createdAt: Value(createdAt),
    );
    return _database.into(_database.bloodPressures).insert(companion);
  }

  /// 更新血压记录
  Future<bool> updateBloodPressure(BloodPressure entry) {
    return _database.update(_database.bloodPressures).replace(entry);
  }

  /// 删除血压记录
  Future<int> deleteBloodPressure(int id) {
    return (_database.delete(_database.bloodPressures)..where((t) => t.id.equals(id))).go();
  }

  /// 批量删除血压记录
  Future<int> deleteBloodPressures(List<int> ids) {
    return (_database.delete(_database.bloodPressures)..where((t) => t.id.isIn(ids))).go();
  }

  /// 根据日期范围获取血压记录
  Stream<List<BloodPressure>> getBloodPressuresByDateRange(DateTime startDate, DateTime endDate) {
    return (_database.select(_database.bloodPressures)
          ..where((t) => t.createdAt.isBetweenValues(startDate, endDate))
          ..orderBy([(t) => OrderingTerm.desc(t.createdAt)]))
        .watch();
  }

  /// 获取最近的血压记录
  Future<BloodPressure?> getLatestBloodPressure() {
    return (_database.select(_database.bloodPressures)
          ..orderBy([(t) => OrderingTerm.desc(t.createdAt)])
          ..limit(1))
        .getSingleOrNull();
  }
}

/// 血压记录仓库提供者
@riverpod
BloodPressureRepository bloodPressureRepository(Ref ref) {
  final database = ref.watch(databaseProvider);
  return BloodPressureRepository(database);
}
