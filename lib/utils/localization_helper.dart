import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

/// 本地化帮助类
/// 提供便捷的国际化功能
class LocalizationHelper {
  /// 获取当前语言代码
  static String getCurrentLanguageCode(BuildContext context) {
    return context.locale.languageCode;
  }

  /// 获取当前语言名称
  static String getCurrentLanguageName(BuildContext context) {
    final currentLocale = context.locale;
    switch (currentLocale.languageCode) {
      case 'zh':
        return '中文';
      case 'en':
        return 'English';
      default:
        return currentLocale.languageCode;
    }
  }

  /// 切换到中文
  static void switchToChinese(BuildContext context) {
    context.setLocale(const Locale('zh'));
  }

  /// 切换到英文
  static void switchToEnglish(BuildContext context) {
    context.setLocale(const Locale('en'));
  }

  /// 获取支持的语言列表
  static List<Locale> getSupportedLocales() {
    return const [Locale('zh'), Locale('en')];
  }

  /// 检查是否为中文环境
  static bool isChinese(BuildContext context) {
    return context.locale.languageCode == 'zh';
  }

  /// 检查是否为英文环境
  static bool isEnglish(BuildContext context) {
    return context.locale.languageCode == 'en';
  }
}