import 'dart:async';

/// 防抖工具类
/// 用于防止函数被频繁调用，确保在指定时间内只执行一次
class Debounce {
  /// 要执行的回调函数
  final FutureOr<void> Function() _callback;
  
  /// 防抖延迟时间
  final Duration _duration;
  
  /// 是否正在执行中
  bool _isExecuting = false;
  
  /// 构造函数
  /// [callback] 要执行的回调函数
  /// [duration] 防抖延迟时间，默认1秒
  Debounce(
    this._callback, {
    Duration duration = const Duration(milliseconds: 1000),
  }) : _duration = duration;
  
  /// 调用函数
  /// 如果当前没有正在执行，则执行回调函数
  /// 执行完成后会等待指定的延迟时间
  Future<void> call() async {
    // 如果正在执行中，直接返回
    if (_isExecuting) {
      return;
    }
    
    // 设置执行状态
    _isExecuting = true;
    
    try {
      // 执行回调函数
      await _callback();
    } finally {
      // 等待防抖延迟时间
      await Future.delayed(_duration);
      // 重置执行状态
      _isExecuting = false;
    }
  }
  
  /// 获取当前是否正在执行
  bool get isExecuting => _isExecuting;
  
  /// 重置状态（强制停止防抖）
  void reset() {
    _isExecuting = false;
  }
}