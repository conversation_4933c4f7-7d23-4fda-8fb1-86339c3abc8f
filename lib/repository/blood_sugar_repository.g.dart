// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'blood_sugar_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$bloodSugarRepositoryHash() =>
    r'57c54da4702d2578076c1221c4b8f472de837ab1';

/// 血糖记录仓库提供者
///
/// Copied from [bloodSugarRepository].
@ProviderFor(bloodSugarRepository)
final bloodSugarRepositoryProvider =
    AutoDisposeProvider<BloodSugarRepository>.internal(
      bloodSugarRepository,
      name: r'bloodSugarRepositoryProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$bloodSugarRepositoryHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef BloodSugarRepositoryRef = AutoDisposeProviderRef<BloodSugarRepository>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
