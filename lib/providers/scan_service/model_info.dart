part of 'scan_provider.dart';

/// 管理TensorFlow Lite模型的信息和配置
class ModelInfo {
  final List<List<int>> inputShape;
  final List<List<int>> outputShape;
  final TensorType inputType;
  final TensorType outputType;
  
  const ModelInfo({
    required this.inputShape,
    required this.outputShape,
    required this.inputType,
    required this.outputType,
  });
  
  /// 获取输入图像的高度
  int get inputHeight => inputShape[0][1];
  
  /// 获取输入图像的宽度
  int get inputWidth => inputShape[0][2];
  
  /// 获取输入图像的通道数
  int get inputChannels => inputShape[0][3];
  
  /// 获取输入张量的总大小
  int get inputSize => inputHeight * inputWidth * inputChannels;
  
  /// 从解释器创建ModelInfo实例
  static ModelInfo fromInterpreter(Interpreter interpreter) {
    try {
      final inputShape = interpreter.getInputTensors().map((tensor) => tensor.shape).toList();
      final outputShape = interpreter.getOutputTensors().map((tensor) => tensor.shape).toList();
      final inputType = interpreter.getInputTensors().first.type;
      final outputType = interpreter.getOutputTensors().first.type;
      
      return ModelInfo(
        inputShape: inputShape,
        outputShape: outputShape,
        inputType: inputType,
        outputType: outputType,
      );
    } catch (e) {
      debugPrint('Error getting model info: $e');
      rethrow;
    }
  }
  
  @override
  String toString() {
    return 'ModelInfo(inputShape: $inputShape, outputShape: $outputShape, inputType: $inputType, outputType: $outputType)';
  }
}