part of 'scan_provider.dart';

/// 调试辅助工具类
class DebugHelper {
  static const bool _isDebugMode = kDebugMode;
  
  
  /// 保存调试数据到临时文件（仅在调试模式下）
  static Future<String?> saveDebugData(Uint8List data, String fileName) async {
    try {
      final tempDir = Directory.systemTemp;
      final tempFilePath = '${tempDir.path}/$fileName';
      final tempFile = File(tempFilePath);
      
      await tempFile.writeAsBytes(data);
      return tempFilePath;
    } catch (e) {
      return null;
    }
  }
  
  /// 保存Float32List调试数据
  static Future<String?> saveFloat32ListDebugData(Float32List data, String fileName) async {
    if (!_isDebugMode) return null;
    
    try {
      final byteData = ByteData(data.length * 4);
      for (var i = 0; i < data.length; i++) {
        byteData.setFloat32(i * 4, data[i], Endian.little);
      }
      
      return await saveDebugData(byteData.buffer.asUint8List(), fileName);
    } catch (e) {
      return null;
    }
  }
}