# 最近记录功能使用示例

## 重构后的架构

### 1. RecentRecordsController
- **位置**: `lib/pages/home/<USER>/recent_records_controller.dart`
- **类型**: `Notifier<RecentRecordsState>`
- **功能**: 管理所有分页状态和逻辑

#### 状态管理
```dart
class RecentRecordsState {
  final List<HealthRecordEntry> records;  // 当前已加载的记录
  final int currentPage;                  // 当前页码
  final bool isLoading;                   // 初始加载状态
  final bool isLoadingMore;               // 加载更多状态
  final bool hasMore;                     // 是否还有更多数据
  final String? error;                    // 错误信息
  final int totalCount;                   // 总记录数
}
```

#### 核心方法
- `loadMore()`: 加载下一页数据
- `refresh()`: 刷新所有数据
- `_getAllRecords()`: 获取并合并血压血糖记录
- `_getPageRecords()`: 分页处理

### 2. RecentRecordsList Widget
- **位置**: `lib/pages/home/<USER>/recent_records_list.dart`
- **类型**: `ConsumerStatefulWidget`
- **功能**: 纯 UI 组件，响应状态变化

#### 简化后的职责
- 监听滚动事件
- 调用 controller 的方法
- 根据状态渲染不同的 UI

## 使用方式

### 在页面中使用
```dart
// 直接使用，无需传递参数
const RecentRecordsList()
```

### 手动触发操作
```dart
// 刷新数据
ref.read(recentRecordsControllerProvider.notifier).refresh();

// 加载更多
ref.read(recentRecordsControllerProvider.notifier).loadMore();

// 监听状态
final state = ref.watch(recentRecordsControllerProvider);
```

## 优势

1. **状态集中管理**: 所有分页相关状态都在 controller 中
2. **逻辑复用**: controller 可以在多个地方使用
3. **测试友好**: 状态和逻辑分离，易于单元测试
4. **性能优化**: 内置缓存机制，避免重复请求
5. **用户体验**: 支持下拉刷新、无限滚动、错误重试

## 技术特性

- ✅ 无限滚动分页
- ✅ 下拉刷新
- ✅ 加载状态管理
- ✅ 错误处理和重试
- ✅ 数据缓存（30秒超时）
- ✅ 空状态显示
- ✅ 类型安全的状态管理
- ✅ 内存优化
