import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:health_diary/pages/add_record/controller/add_record_controller.dart';
import 'package:health_diary/providers/health_type_provider.dart';
import 'package:health_diary/types/health_types.dart';

class RecordTypeTabs extends ConsumerWidget implements PreferredSizeWidget {
  final List<HealthRecordTypeEnum> types;

  const RecordTypeTabs({super.key, required this.types});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return TabBar(
      isScrollable: true,
      tabAlignment: TabAlignment.start,
      padding: const EdgeInsets.symmetric(horizontal: 0),
      dividerColor: Colors.transparent,
      onTap: (type) => _onTap(ref, type),
      tabs: types
          .map(
            (type) => Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Tab(text: ref.read(healthTypeProvider).getTypeName(type)),
            ),
          )
          .toList(),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(48);

  void _onTap(WidgetRef ref, int index) {
    ref.read(addRecordControllerProvider.notifier).setRecordType(types[index]);
  }
}
