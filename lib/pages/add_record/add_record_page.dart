import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:health_diary/pages/add_record/controller/data_controller.dart';
import 'package:health_diary/providers/health_type_provider.dart';

import '../../themes/app_theme.dart';
import 'controller/add_record_controller.dart';
import 'widgets/input_form.dart';
import 'widgets/scan_widget.dart';
import 'widgets/record_type_tabs.dart';

class AddRecordPage extends ConsumerStatefulWidget {
  const AddRecordPage({super.key});

  @override
  ConsumerState<AddRecordPage> createState() => _AddRecordPageState();
}

class _AddRecordPageState extends ConsumerState<AddRecordPage> {
  @override
  Widget build(BuildContext context) {
    final inputType = ref.watch(addRecordControllerProvider.select((p) => p.inputType));
    final healthTypes = ref.read(healthTypeProvider).types;
    final dataController = ref.read(dataControllerProvider.notifier);

    return DefaultTabController(
      length: healthTypes.length,
      child: Scaffold(
        resizeToAvoidBottomInset: true,
        appBar: AppBar(
          elevation: 0,
          backgroundColor: Theme.of(context).appBarTheme.backgroundColor,
          title: Text('add_record'.tr()),
          bottom: RecordTypeTabs(types: healthTypes),
          flexibleSpace: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomLeft,
                colors: context.appColors.backgroundGradient,
                stops: const [0, 0.2, 0.5, 1],
              ),
            ),
          ),
        ),
        body: GestureDetector(
          onTap: () => FocusScope.of(context).unfocus(),
          child: TabBarView(
            children: healthTypes.map((recordType) {
              return SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: !dataController.canScan(recordType) || inputType == AddRecordInputType.manual
                      ? InputForm(recordType: recordType)
                      : const ScanWidget(),
                ),
              );
            }).toList(),
          ),
        ),
      ),
     );
  }
}
