import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:easy_localization/easy_localization.dart';

import 'package:health_diary/themes/app_theme.dart';
import 'package:health_diary/types/health_record.dart';
import 'health_metric_widget.dart';

/// 今日健康概览卡片
class TodayHealthCard extends StatelessWidget {
  final TodayHealthOverview overview;

  const TodayHealthCard({super.key, required this.overview});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.secondary,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('today_health'.tr(), style: Theme.of(context).textTheme.titleMedium),
              FaIcon(FontAwesomeIcons.heartPulse, color: Theme.of(context).colorScheme.onSurfaceVariant, size: 20),
            ],
          ),
          const SizedBox(height: 12),
          Text(_getLocalizedStatus(overview.status), style: Theme.of(context).textTheme.headlineMedium),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                child: HealthMetricWidget(
                  label: 'blood_pressure'.tr(),
                  value: overview.bloodPressure,
                  unit: overview.bloodPressureUnit,
                  icon: FaIcon(
                    FontAwesomeIcons.heartPulse,
                    size: 16,
                    color: context.appColors.heartIcon.withValues(alpha: 0.6),
                  ),
                ),
              ),
              const SizedBox(width: 24),
              Expanded(
                child: HealthMetricWidget(
                  label: 'blood_sugar'.tr(),
                  value: overview.bloodSugar,
                  unit: overview.bloodSugarUnit,
                  icon: FaIcon(
                    FontAwesomeIcons.droplet,
                    size: 16,
                    color: context.appColors.dropletIcon.withValues(alpha: 0.6),
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 20),
        ],
      ),
    );
  }

  String _getLocalizedStatus(String status) {
    switch (status) {
      case '良好':
        return 'status_good'.tr();
      default:
        return status;
    }
  }
}
