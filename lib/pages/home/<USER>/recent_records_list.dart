import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:easy_localization/easy_localization.dart';

import '../../../themes/app_theme.dart';
import '../../../types/health_record.dart';
import '../controller/recent_records_controller.dart';
import 'data_item_widget.dart';

/// 最近记录列表
class RecentRecordsList extends ConsumerStatefulWidget {
  const RecentRecordsList({super.key});

  @override
  ConsumerState<RecentRecordsList> createState() => _RecentRecordsListState();
}

class _RecentRecordsListState extends ConsumerState<RecentRecordsList> {

  /// 刷新数据
  Future<void> _refreshData() async {
    await ref.read(recentRecordsControllerProvider.notifier).refresh();
  }

  @override
  Widget build(BuildContext context) {
    final asyncRecords = ref.watch(recentRecordsControllerProvider);

    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'recent_records'.tr(),
                style: Theme.of(context).textTheme.titleLarge,
              ),
            ],
          ),
        ),
        const SizedBox(height: 8),
        Expanded(
          child: RefreshIndicator(
            onRefresh: _refreshData,
            child: _buildAsyncContent(asyncRecords),
          ),
        ),
      ],
    );
  }

  /// 构建异步内容
  Widget _buildAsyncContent(AsyncValue<List<HealthRecordEntry>> asyncRecords) {
    return asyncRecords.when(
      data: (records) {
        if (records.isEmpty) {
          return _buildEmptyState();
        }
        return _buildRecordsList(records);
      },
      loading: () => _buildLoadingIndicator(),
      error: (error, stackTrace) => _buildErrorState(error.toString()),
    );
  }

  /// 构建记录列表
  Widget _buildRecordsList(List<HealthRecordEntry> records) {
    final controller = ref.read(recentRecordsControllerProvider.notifier);
    final hasMore = controller.hasMore;

    return ListView.builder(
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      padding: EdgeInsets.zero,
      itemCount: records.length + (hasMore ? 1 : 0),
      itemBuilder: (context, index) {
        // 如果是最后一个item且还有更多数据，显示加载指示器并触发加载
        if (index == records.length) {
          // 触发加载更多
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (hasMore && !controller.isLoading) {
              controller.loadMore();
            }
          });
          return null;
        }

        return Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: RecordItem(record: records[index]),
        );
      },
    );
  }

  /// 构建空状态
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.inbox_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'no_records_yet'.tr(),
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建错误状态
  Widget _buildErrorState(String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red[400],
          ),
          const SizedBox(height: 16),
          Text(
            error,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Colors.red[600],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              ref.read(recentRecordsControllerProvider.notifier).refresh();
            },
            child: Text('retry'.tr()),
          ),
        ],
      ),
    );
  }

  /// 构建加载指示器
  Widget _buildLoadingIndicator() {
    return const Padding(
      padding: EdgeInsets.all(16.0),
      child: Center(
        child: CircularProgressIndicator(),
      ),
    );
  }
}

/// 根据记录颜色值获取对应的主题颜色
Color _getRecordCardColor(BuildContext context, int colorValue) {
  switch (colorValue) {
    case 0xFFFFEBF0:
      return context.appColors.bloodPressureCard;
    case 0xFFE4F4FF:
      return context.appColors.bloodSugarCard;
    default:
      return Color(colorValue);
  }
}

/// 记录条目
class RecordItem extends StatelessWidget {
  final HealthRecordEntry record;

  const RecordItem({super.key, required this.record});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _getRecordCardColor(context, record.color),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          // 顶部：左上角时间，右上角记录类型
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                _getLocalizedType(record.type),
                style: Theme.of(context).textTheme.titleMedium,
              ),
              Text(
                _getLocalizedTime(record.time),
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
          ),
          const SizedBox(height: 12),
          // 数据显示区域
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: record.values.map((value) => DataItemWidget(
              label: _getLocalizedLabel(value.label),
              value: value.value,
              unit: value.unit,
            )).toList(),
          ),
        ],
      ),
    );
  }

  String _getLocalizedType(String type) {
    switch (type) {
      case '血压':
        return 'blood_pressure'.tr();
      case '血糖':
        return 'blood_sugar'.tr();
      default:
        return type;
    }
  }

  String _getLocalizedTime(String time) {
    switch (time) {
      case '今天 08:30':
        return 'today_time'.tr();
      case '昨天 18:45':
        return 'yesterday_evening'.tr();
      case '昨天 08:15':
        return 'yesterday_morning'.tr();
      default:
        return time;
    }
  }

  String _getLocalizedLabel(String label) {
    switch (label) {
      case '高压':
        return 'systolic'.tr();
      case '低压':
        return 'diastolic'.tr();
      case '脉搏':
        return 'pulse'.tr();
      case '血糖':
        return 'blood_sugar'.tr();
      default:
        return label;
    }
  }
}
