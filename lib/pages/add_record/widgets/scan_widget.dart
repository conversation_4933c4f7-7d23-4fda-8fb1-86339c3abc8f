import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'dart:typed_data';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:camera/camera.dart';
import 'package:health_diary/pages/add_record/controller/add_record_controller.dart';
import 'package:health_diary/providers/camera_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:health_diary/utils/debounce.dart';

class ScanWidget extends ConsumerStatefulWidget {
  const ScanWidget({super.key});

  @override
  ConsumerState<ScanWidget> createState() => _ScanWidgetState();
}

class _ScanWidgetState extends ConsumerState<ScanWidget> {
  final GlobalKey _repaintBoundaryKey = GlobalKey();
  bool _isScaning = false;

  @override
  void initState() {
    debugPrint('init ScanWidget');
    super.initState();
    
  }

  @override
  void dispose() {
    debugPrint('dispose ScanWidget');
    super.dispose();
  }

  void _onCapturePressed() async {
    final cameraController = ref.read(cameraControllerNotifierProvider).value;
    if (cameraController == null) {
      return;
    }
    setState(() {
      _isScaning = true;
    });

    await cameraController.pausePreview();
    try {
      final renderObj = _repaintBoundaryKey.currentContext?.findRenderObject();
      final boundary = renderObj as RenderRepaintBoundary?;
      
      // 截图
      final image = await boundary?.toImage(pixelRatio: 3);
      ByteData? byteData = await image?.toByteData(format: ui.ImageByteFormat.png);
      
      if (byteData != null) {
        final imageData = byteData.buffer.asUint8List();
        await ref.read(addRecordControllerProvider.notifier).scan(imageData: imageData);
      }
    } catch (e) {
      debugPrint('Error during screenshot: $e');
    } finally {
      
      setState(() {
        _isScaning = false;
      });
      cameraController.resumePreview();
    }
  }

  Widget _buildScanErrorWidget() {
    final addRecordState = ref.watch(addRecordControllerProvider);
    
    if (!addRecordState.hasScanError) {
      return const SizedBox.shrink();
    }
    
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            'scan_recognition_failed'.tr(),
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.error,
            ),
          ),
          const SizedBox(width: 8),
          GestureDetector(
            onTap: () {
              ref.read(addRecordControllerProvider.notifier).clearScanError();
            },
            child: Icon(
              Icons.close,
              color: Theme.of(context).colorScheme.error,
              size: 18,
            ),
          ),
        ],
      ),
    );
  }

  void _onManualInputPressed() {
    ref.read(addRecordControllerProvider.notifier).setInputType(AddRecordInputType.manual);
  }


  Widget _buildCameraContent(AsyncValue<CameraController?> cameraAsyncValue) {
    return cameraAsyncValue.when(
      data: (controller) {
        if (controller != null && controller.value.isInitialized) {
          // 使用RepaintBoundary包装相机预览以支持截图
          return RepaintBoundary(
            key: _repaintBoundaryKey,
            child: FittedBox(
              fit: BoxFit.cover,
              child: SizedBox(
                width: controller.value.previewSize!.height,
                height: controller.value.previewSize!.width,
                child: controller.buildPreview(),
              ),
            ),
          );
        } else {
          return _buildErrorContent('camera_permission_required'.tr());
        }
      },
      loading: () {
        return const Center(child: CircularProgressIndicator());
      },
      error: (error, stackTrace) {
        debugPrint('Camera initialization error: $error');
        return _buildErrorContent('camera_permission_required'.tr());
      },
    );
  }

  Widget _buildErrorContent(String errorMessage) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.camera_alt_outlined, size: 48, color: Theme.of(context).colorScheme.error),
            const SizedBox(height: 16),
            Text(errorMessage, style: Theme.of(context).textTheme.titleMedium, textAlign: TextAlign.center),
            const SizedBox(height: 16),
            TextButton(
              onPressed: () async {
                await openAppSettings();
              },
              child: Text('go_to_camera_permission_settings'.tr()),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final cameraAsyncValue = ref.watch(cameraControllerNotifierProvider);
    return Column(
      children: [
        DottedBorder(
          options: const RoundedRectDottedBorderOptions(
            radius: Radius.circular(12),
            color: Color(0xFF4CAF50),
            strokeWidth: 1,
            dashPattern: [6, 6],
          ),
          child: AspectRatio(
            aspectRatio: 1,
            child: Container(
              width: double.infinity,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withAlpha(15),
                borderRadius: BorderRadius.circular(12),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: _buildCameraContent(cameraAsyncValue),
              ),
            ),
          ),
        ),
        const SizedBox(height: 24),
        _buildScanErrorWidget(),
        Text('scan_measurement_data'.tr(), style: Theme.of(context).textTheme.titleLarge),
        const SizedBox(height: 8),
        Text(
          'scan_measurement_data_desc'.tr(),
          style: Theme.of(context).textTheme.labelMedium?.copyWith(fontWeight: FontWeight.w500),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 32),
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: Debounce(_onCapturePressed).call,
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.primary,
              foregroundColor: Theme.of(context).colorScheme.onPrimary,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text('recognize'.tr()),
                if (_isScaning) ...[
                  const SizedBox(width: 8),
                  const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  ),
                ],
              ],
            )
          ),
        ),
        const SizedBox(height: 8),
        TextButton.icon(
          onPressed: Debounce(_onManualInputPressed).call,
          icon: const Icon(Icons.edit_outlined, size: 18),
          label: Text('manual_input'.tr()),
        ),
      ],
    );
  }
}
