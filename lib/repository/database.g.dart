// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'database.dart';

// ignore_for_file: type=lint
class $BloodPressuresTable extends BloodPressures
    with TableInfo<$BloodPressuresTable, BloodPressure> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $BloodPressuresTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
    'id',
    aliasedName,
    false,
    hasAutoIncrement: true,
    type: DriftSqlType.int,
    requiredDuringInsert: false,
    defaultConstraints: GeneratedColumn.constraintIsAlways(
      'PRIMARY KEY AUTOINCREMENT',
    ),
  );
  static const VerificationMeta _systolicMeta = const VerificationMeta(
    'systolic',
  );
  @override
  late final GeneratedColumn<int> systolic = GeneratedColumn<int>(
    'systolic',
    aliasedName,
    false,
    type: DriftSqlType.int,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _diastolicMeta = const VerificationMeta(
    'diastolic',
  );
  @override
  late final GeneratedColumn<int> diastolic = GeneratedColumn<int>(
    'diastolic',
    aliasedName,
    false,
    type: DriftSqlType.int,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _pulseMeta = const VerificationMeta('pulse');
  @override
  late final GeneratedColumn<int> pulse = GeneratedColumn<int>(
    'pulse',
    aliasedName,
    true,
    type: DriftSqlType.int,
    requiredDuringInsert: false,
  );
  static const VerificationMeta _noteMeta = const VerificationMeta('note');
  @override
  late final GeneratedColumn<String> note = GeneratedColumn<String>(
    'note',
    aliasedName,
    true,
    type: DriftSqlType.string,
    requiredDuringInsert: false,
  );
  static const VerificationMeta _createdAtMeta = const VerificationMeta(
    'createdAt',
  );
  @override
  late final GeneratedColumn<DateTime> createdAt = GeneratedColumn<DateTime>(
    'created_at',
    aliasedName,
    false,
    type: DriftSqlType.dateTime,
    requiredDuringInsert: true,
  );
  @override
  List<GeneratedColumn> get $columns => [
    id,
    systolic,
    diastolic,
    pulse,
    note,
    createdAt,
  ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'blood_pressures';
  @override
  VerificationContext validateIntegrity(
    Insertable<BloodPressure> instance, {
    bool isInserting = false,
  }) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('systolic')) {
      context.handle(
        _systolicMeta,
        systolic.isAcceptableOrUnknown(data['systolic']!, _systolicMeta),
      );
    } else if (isInserting) {
      context.missing(_systolicMeta);
    }
    if (data.containsKey('diastolic')) {
      context.handle(
        _diastolicMeta,
        diastolic.isAcceptableOrUnknown(data['diastolic']!, _diastolicMeta),
      );
    } else if (isInserting) {
      context.missing(_diastolicMeta);
    }
    if (data.containsKey('pulse')) {
      context.handle(
        _pulseMeta,
        pulse.isAcceptableOrUnknown(data['pulse']!, _pulseMeta),
      );
    }
    if (data.containsKey('note')) {
      context.handle(
        _noteMeta,
        note.isAcceptableOrUnknown(data['note']!, _noteMeta),
      );
    }
    if (data.containsKey('created_at')) {
      context.handle(
        _createdAtMeta,
        createdAt.isAcceptableOrUnknown(data['created_at']!, _createdAtMeta),
      );
    } else if (isInserting) {
      context.missing(_createdAtMeta);
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  BloodPressure map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return BloodPressure(
      id: attachedDatabase.typeMapping.read(
        DriftSqlType.int,
        data['${effectivePrefix}id'],
      )!,
      systolic: attachedDatabase.typeMapping.read(
        DriftSqlType.int,
        data['${effectivePrefix}systolic'],
      )!,
      diastolic: attachedDatabase.typeMapping.read(
        DriftSqlType.int,
        data['${effectivePrefix}diastolic'],
      )!,
      pulse: attachedDatabase.typeMapping.read(
        DriftSqlType.int,
        data['${effectivePrefix}pulse'],
      ),
      note: attachedDatabase.typeMapping.read(
        DriftSqlType.string,
        data['${effectivePrefix}note'],
      ),
      createdAt: attachedDatabase.typeMapping.read(
        DriftSqlType.dateTime,
        data['${effectivePrefix}created_at'],
      )!,
    );
  }

  @override
  $BloodPressuresTable createAlias(String alias) {
    return $BloodPressuresTable(attachedDatabase, alias);
  }
}

class BloodPressure extends DataClass implements Insertable<BloodPressure> {
  final int id;
  final int systolic;
  final int diastolic;
  final int? pulse;
  final String? note;
  final DateTime createdAt;
  const BloodPressure({
    required this.id,
    required this.systolic,
    required this.diastolic,
    this.pulse,
    this.note,
    required this.createdAt,
  });
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    map['systolic'] = Variable<int>(systolic);
    map['diastolic'] = Variable<int>(diastolic);
    if (!nullToAbsent || pulse != null) {
      map['pulse'] = Variable<int>(pulse);
    }
    if (!nullToAbsent || note != null) {
      map['note'] = Variable<String>(note);
    }
    map['created_at'] = Variable<DateTime>(createdAt);
    return map;
  }

  BloodPressuresCompanion toCompanion(bool nullToAbsent) {
    return BloodPressuresCompanion(
      id: Value(id),
      systolic: Value(systolic),
      diastolic: Value(diastolic),
      pulse: pulse == null && nullToAbsent
          ? const Value.absent()
          : Value(pulse),
      note: note == null && nullToAbsent ? const Value.absent() : Value(note),
      createdAt: Value(createdAt),
    );
  }

  factory BloodPressure.fromJson(
    Map<String, dynamic> json, {
    ValueSerializer? serializer,
  }) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return BloodPressure(
      id: serializer.fromJson<int>(json['id']),
      systolic: serializer.fromJson<int>(json['systolic']),
      diastolic: serializer.fromJson<int>(json['diastolic']),
      pulse: serializer.fromJson<int?>(json['pulse']),
      note: serializer.fromJson<String?>(json['note']),
      createdAt: serializer.fromJson<DateTime>(json['createdAt']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'systolic': serializer.toJson<int>(systolic),
      'diastolic': serializer.toJson<int>(diastolic),
      'pulse': serializer.toJson<int?>(pulse),
      'note': serializer.toJson<String?>(note),
      'createdAt': serializer.toJson<DateTime>(createdAt),
    };
  }

  BloodPressure copyWith({
    int? id,
    int? systolic,
    int? diastolic,
    Value<int?> pulse = const Value.absent(),
    Value<String?> note = const Value.absent(),
    DateTime? createdAt,
  }) => BloodPressure(
    id: id ?? this.id,
    systolic: systolic ?? this.systolic,
    diastolic: diastolic ?? this.diastolic,
    pulse: pulse.present ? pulse.value : this.pulse,
    note: note.present ? note.value : this.note,
    createdAt: createdAt ?? this.createdAt,
  );
  BloodPressure copyWithCompanion(BloodPressuresCompanion data) {
    return BloodPressure(
      id: data.id.present ? data.id.value : this.id,
      systolic: data.systolic.present ? data.systolic.value : this.systolic,
      diastolic: data.diastolic.present ? data.diastolic.value : this.diastolic,
      pulse: data.pulse.present ? data.pulse.value : this.pulse,
      note: data.note.present ? data.note.value : this.note,
      createdAt: data.createdAt.present ? data.createdAt.value : this.createdAt,
    );
  }

  @override
  String toString() {
    return (StringBuffer('BloodPressure(')
          ..write('id: $id, ')
          ..write('systolic: $systolic, ')
          ..write('diastolic: $diastolic, ')
          ..write('pulse: $pulse, ')
          ..write('note: $note, ')
          ..write('createdAt: $createdAt')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode =>
      Object.hash(id, systolic, diastolic, pulse, note, createdAt);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is BloodPressure &&
          other.id == this.id &&
          other.systolic == this.systolic &&
          other.diastolic == this.diastolic &&
          other.pulse == this.pulse &&
          other.note == this.note &&
          other.createdAt == this.createdAt);
}

class BloodPressuresCompanion extends UpdateCompanion<BloodPressure> {
  final Value<int> id;
  final Value<int> systolic;
  final Value<int> diastolic;
  final Value<int?> pulse;
  final Value<String?> note;
  final Value<DateTime> createdAt;
  const BloodPressuresCompanion({
    this.id = const Value.absent(),
    this.systolic = const Value.absent(),
    this.diastolic = const Value.absent(),
    this.pulse = const Value.absent(),
    this.note = const Value.absent(),
    this.createdAt = const Value.absent(),
  });
  BloodPressuresCompanion.insert({
    this.id = const Value.absent(),
    required int systolic,
    required int diastolic,
    this.pulse = const Value.absent(),
    this.note = const Value.absent(),
    required DateTime createdAt,
  }) : systolic = Value(systolic),
       diastolic = Value(diastolic),
       createdAt = Value(createdAt);
  static Insertable<BloodPressure> custom({
    Expression<int>? id,
    Expression<int>? systolic,
    Expression<int>? diastolic,
    Expression<int>? pulse,
    Expression<String>? note,
    Expression<DateTime>? createdAt,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (systolic != null) 'systolic': systolic,
      if (diastolic != null) 'diastolic': diastolic,
      if (pulse != null) 'pulse': pulse,
      if (note != null) 'note': note,
      if (createdAt != null) 'created_at': createdAt,
    });
  }

  BloodPressuresCompanion copyWith({
    Value<int>? id,
    Value<int>? systolic,
    Value<int>? diastolic,
    Value<int?>? pulse,
    Value<String?>? note,
    Value<DateTime>? createdAt,
  }) {
    return BloodPressuresCompanion(
      id: id ?? this.id,
      systolic: systolic ?? this.systolic,
      diastolic: diastolic ?? this.diastolic,
      pulse: pulse ?? this.pulse,
      note: note ?? this.note,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (systolic.present) {
      map['systolic'] = Variable<int>(systolic.value);
    }
    if (diastolic.present) {
      map['diastolic'] = Variable<int>(diastolic.value);
    }
    if (pulse.present) {
      map['pulse'] = Variable<int>(pulse.value);
    }
    if (note.present) {
      map['note'] = Variable<String>(note.value);
    }
    if (createdAt.present) {
      map['created_at'] = Variable<DateTime>(createdAt.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('BloodPressuresCompanion(')
          ..write('id: $id, ')
          ..write('systolic: $systolic, ')
          ..write('diastolic: $diastolic, ')
          ..write('pulse: $pulse, ')
          ..write('note: $note, ')
          ..write('createdAt: $createdAt')
          ..write(')'))
        .toString();
  }
}

class $BloodSugarsTable extends BloodSugars
    with TableInfo<$BloodSugarsTable, BloodSugar> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $BloodSugarsTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
    'id',
    aliasedName,
    false,
    hasAutoIncrement: true,
    type: DriftSqlType.int,
    requiredDuringInsert: false,
    defaultConstraints: GeneratedColumn.constraintIsAlways(
      'PRIMARY KEY AUTOINCREMENT',
    ),
  );
  static const VerificationMeta _valueMeta = const VerificationMeta('value');
  @override
  late final GeneratedColumn<double> value = GeneratedColumn<double>(
    'value',
    aliasedName,
    false,
    type: DriftSqlType.double,
    requiredDuringInsert: true,
  );
  static const VerificationMeta _noteMeta = const VerificationMeta('note');
  @override
  late final GeneratedColumn<String> note = GeneratedColumn<String>(
    'note',
    aliasedName,
    true,
    type: DriftSqlType.string,
    requiredDuringInsert: false,
  );
  static const VerificationMeta _createdAtMeta = const VerificationMeta(
    'createdAt',
  );
  @override
  late final GeneratedColumn<DateTime> createdAt = GeneratedColumn<DateTime>(
    'created_at',
    aliasedName,
    false,
    type: DriftSqlType.dateTime,
    requiredDuringInsert: true,
  );
  @override
  List<GeneratedColumn> get $columns => [id, value, note, createdAt];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'blood_sugars';
  @override
  VerificationContext validateIntegrity(
    Insertable<BloodSugar> instance, {
    bool isInserting = false,
  }) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('value')) {
      context.handle(
        _valueMeta,
        value.isAcceptableOrUnknown(data['value']!, _valueMeta),
      );
    } else if (isInserting) {
      context.missing(_valueMeta);
    }
    if (data.containsKey('note')) {
      context.handle(
        _noteMeta,
        note.isAcceptableOrUnknown(data['note']!, _noteMeta),
      );
    }
    if (data.containsKey('created_at')) {
      context.handle(
        _createdAtMeta,
        createdAt.isAcceptableOrUnknown(data['created_at']!, _createdAtMeta),
      );
    } else if (isInserting) {
      context.missing(_createdAtMeta);
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  BloodSugar map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return BloodSugar(
      id: attachedDatabase.typeMapping.read(
        DriftSqlType.int,
        data['${effectivePrefix}id'],
      )!,
      value: attachedDatabase.typeMapping.read(
        DriftSqlType.double,
        data['${effectivePrefix}value'],
      )!,
      note: attachedDatabase.typeMapping.read(
        DriftSqlType.string,
        data['${effectivePrefix}note'],
      ),
      createdAt: attachedDatabase.typeMapping.read(
        DriftSqlType.dateTime,
        data['${effectivePrefix}created_at'],
      )!,
    );
  }

  @override
  $BloodSugarsTable createAlias(String alias) {
    return $BloodSugarsTable(attachedDatabase, alias);
  }
}

class BloodSugar extends DataClass implements Insertable<BloodSugar> {
  final int id;
  final double value;
  final String? note;
  final DateTime createdAt;
  const BloodSugar({
    required this.id,
    required this.value,
    this.note,
    required this.createdAt,
  });
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    map['value'] = Variable<double>(value);
    if (!nullToAbsent || note != null) {
      map['note'] = Variable<String>(note);
    }
    map['created_at'] = Variable<DateTime>(createdAt);
    return map;
  }

  BloodSugarsCompanion toCompanion(bool nullToAbsent) {
    return BloodSugarsCompanion(
      id: Value(id),
      value: Value(value),
      note: note == null && nullToAbsent ? const Value.absent() : Value(note),
      createdAt: Value(createdAt),
    );
  }

  factory BloodSugar.fromJson(
    Map<String, dynamic> json, {
    ValueSerializer? serializer,
  }) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return BloodSugar(
      id: serializer.fromJson<int>(json['id']),
      value: serializer.fromJson<double>(json['value']),
      note: serializer.fromJson<String?>(json['note']),
      createdAt: serializer.fromJson<DateTime>(json['createdAt']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'value': serializer.toJson<double>(value),
      'note': serializer.toJson<String?>(note),
      'createdAt': serializer.toJson<DateTime>(createdAt),
    };
  }

  BloodSugar copyWith({
    int? id,
    double? value,
    Value<String?> note = const Value.absent(),
    DateTime? createdAt,
  }) => BloodSugar(
    id: id ?? this.id,
    value: value ?? this.value,
    note: note.present ? note.value : this.note,
    createdAt: createdAt ?? this.createdAt,
  );
  BloodSugar copyWithCompanion(BloodSugarsCompanion data) {
    return BloodSugar(
      id: data.id.present ? data.id.value : this.id,
      value: data.value.present ? data.value.value : this.value,
      note: data.note.present ? data.note.value : this.note,
      createdAt: data.createdAt.present ? data.createdAt.value : this.createdAt,
    );
  }

  @override
  String toString() {
    return (StringBuffer('BloodSugar(')
          ..write('id: $id, ')
          ..write('value: $value, ')
          ..write('note: $note, ')
          ..write('createdAt: $createdAt')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(id, value, note, createdAt);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is BloodSugar &&
          other.id == this.id &&
          other.value == this.value &&
          other.note == this.note &&
          other.createdAt == this.createdAt);
}

class BloodSugarsCompanion extends UpdateCompanion<BloodSugar> {
  final Value<int> id;
  final Value<double> value;
  final Value<String?> note;
  final Value<DateTime> createdAt;
  const BloodSugarsCompanion({
    this.id = const Value.absent(),
    this.value = const Value.absent(),
    this.note = const Value.absent(),
    this.createdAt = const Value.absent(),
  });
  BloodSugarsCompanion.insert({
    this.id = const Value.absent(),
    required double value,
    this.note = const Value.absent(),
    required DateTime createdAt,
  }) : value = Value(value),
       createdAt = Value(createdAt);
  static Insertable<BloodSugar> custom({
    Expression<int>? id,
    Expression<double>? value,
    Expression<String>? note,
    Expression<DateTime>? createdAt,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (value != null) 'value': value,
      if (note != null) 'note': note,
      if (createdAt != null) 'created_at': createdAt,
    });
  }

  BloodSugarsCompanion copyWith({
    Value<int>? id,
    Value<double>? value,
    Value<String?>? note,
    Value<DateTime>? createdAt,
  }) {
    return BloodSugarsCompanion(
      id: id ?? this.id,
      value: value ?? this.value,
      note: note ?? this.note,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (value.present) {
      map['value'] = Variable<double>(value.value);
    }
    if (note.present) {
      map['note'] = Variable<String>(note.value);
    }
    if (createdAt.present) {
      map['created_at'] = Variable<DateTime>(createdAt.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('BloodSugarsCompanion(')
          ..write('id: $id, ')
          ..write('value: $value, ')
          ..write('note: $note, ')
          ..write('createdAt: $createdAt')
          ..write(')'))
        .toString();
  }
}

abstract class _$HealthDiaryDatabase extends GeneratedDatabase {
  _$HealthDiaryDatabase(QueryExecutor e) : super(e);
  $HealthDiaryDatabaseManager get managers => $HealthDiaryDatabaseManager(this);
  late final $BloodPressuresTable bloodPressures = $BloodPressuresTable(this);
  late final $BloodSugarsTable bloodSugars = $BloodSugarsTable(this);
  @override
  Iterable<TableInfo<Table, Object?>> get allTables =>
      allSchemaEntities.whereType<TableInfo<Table, Object?>>();
  @override
  List<DatabaseSchemaEntity> get allSchemaEntities => [
    bloodPressures,
    bloodSugars,
  ];
}

typedef $$BloodPressuresTableCreateCompanionBuilder =
    BloodPressuresCompanion Function({
      Value<int> id,
      required int systolic,
      required int diastolic,
      Value<int?> pulse,
      Value<String?> note,
      required DateTime createdAt,
    });
typedef $$BloodPressuresTableUpdateCompanionBuilder =
    BloodPressuresCompanion Function({
      Value<int> id,
      Value<int> systolic,
      Value<int> diastolic,
      Value<int?> pulse,
      Value<String?> note,
      Value<DateTime> createdAt,
    });

class $$BloodPressuresTableFilterComposer
    extends Composer<_$HealthDiaryDatabase, $BloodPressuresTable> {
  $$BloodPressuresTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<int> get id => $composableBuilder(
    column: $table.id,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<int> get systolic => $composableBuilder(
    column: $table.systolic,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<int> get diastolic => $composableBuilder(
    column: $table.diastolic,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<int> get pulse => $composableBuilder(
    column: $table.pulse,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get note => $composableBuilder(
    column: $table.note,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<DateTime> get createdAt => $composableBuilder(
    column: $table.createdAt,
    builder: (column) => ColumnFilters(column),
  );
}

class $$BloodPressuresTableOrderingComposer
    extends Composer<_$HealthDiaryDatabase, $BloodPressuresTable> {
  $$BloodPressuresTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<int> get id => $composableBuilder(
    column: $table.id,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<int> get systolic => $composableBuilder(
    column: $table.systolic,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<int> get diastolic => $composableBuilder(
    column: $table.diastolic,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<int> get pulse => $composableBuilder(
    column: $table.pulse,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get note => $composableBuilder(
    column: $table.note,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<DateTime> get createdAt => $composableBuilder(
    column: $table.createdAt,
    builder: (column) => ColumnOrderings(column),
  );
}

class $$BloodPressuresTableAnnotationComposer
    extends Composer<_$HealthDiaryDatabase, $BloodPressuresTable> {
  $$BloodPressuresTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<int> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<int> get systolic =>
      $composableBuilder(column: $table.systolic, builder: (column) => column);

  GeneratedColumn<int> get diastolic =>
      $composableBuilder(column: $table.diastolic, builder: (column) => column);

  GeneratedColumn<int> get pulse =>
      $composableBuilder(column: $table.pulse, builder: (column) => column);

  GeneratedColumn<String> get note =>
      $composableBuilder(column: $table.note, builder: (column) => column);

  GeneratedColumn<DateTime> get createdAt =>
      $composableBuilder(column: $table.createdAt, builder: (column) => column);
}

class $$BloodPressuresTableTableManager
    extends
        RootTableManager<
          _$HealthDiaryDatabase,
          $BloodPressuresTable,
          BloodPressure,
          $$BloodPressuresTableFilterComposer,
          $$BloodPressuresTableOrderingComposer,
          $$BloodPressuresTableAnnotationComposer,
          $$BloodPressuresTableCreateCompanionBuilder,
          $$BloodPressuresTableUpdateCompanionBuilder,
          (
            BloodPressure,
            BaseReferences<
              _$HealthDiaryDatabase,
              $BloodPressuresTable,
              BloodPressure
            >,
          ),
          BloodPressure,
          PrefetchHooks Function()
        > {
  $$BloodPressuresTableTableManager(
    _$HealthDiaryDatabase db,
    $BloodPressuresTable table,
  ) : super(
        TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$BloodPressuresTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$BloodPressuresTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$BloodPressuresTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback:
              ({
                Value<int> id = const Value.absent(),
                Value<int> systolic = const Value.absent(),
                Value<int> diastolic = const Value.absent(),
                Value<int?> pulse = const Value.absent(),
                Value<String?> note = const Value.absent(),
                Value<DateTime> createdAt = const Value.absent(),
              }) => BloodPressuresCompanion(
                id: id,
                systolic: systolic,
                diastolic: diastolic,
                pulse: pulse,
                note: note,
                createdAt: createdAt,
              ),
          createCompanionCallback:
              ({
                Value<int> id = const Value.absent(),
                required int systolic,
                required int diastolic,
                Value<int?> pulse = const Value.absent(),
                Value<String?> note = const Value.absent(),
                required DateTime createdAt,
              }) => BloodPressuresCompanion.insert(
                id: id,
                systolic: systolic,
                diastolic: diastolic,
                pulse: pulse,
                note: note,
                createdAt: createdAt,
              ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ),
      );
}

typedef $$BloodPressuresTableProcessedTableManager =
    ProcessedTableManager<
      _$HealthDiaryDatabase,
      $BloodPressuresTable,
      BloodPressure,
      $$BloodPressuresTableFilterComposer,
      $$BloodPressuresTableOrderingComposer,
      $$BloodPressuresTableAnnotationComposer,
      $$BloodPressuresTableCreateCompanionBuilder,
      $$BloodPressuresTableUpdateCompanionBuilder,
      (
        BloodPressure,
        BaseReferences<
          _$HealthDiaryDatabase,
          $BloodPressuresTable,
          BloodPressure
        >,
      ),
      BloodPressure,
      PrefetchHooks Function()
    >;
typedef $$BloodSugarsTableCreateCompanionBuilder =
    BloodSugarsCompanion Function({
      Value<int> id,
      required double value,
      Value<String?> note,
      required DateTime createdAt,
    });
typedef $$BloodSugarsTableUpdateCompanionBuilder =
    BloodSugarsCompanion Function({
      Value<int> id,
      Value<double> value,
      Value<String?> note,
      Value<DateTime> createdAt,
    });

class $$BloodSugarsTableFilterComposer
    extends Composer<_$HealthDiaryDatabase, $BloodSugarsTable> {
  $$BloodSugarsTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<int> get id => $composableBuilder(
    column: $table.id,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<double> get value => $composableBuilder(
    column: $table.value,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<String> get note => $composableBuilder(
    column: $table.note,
    builder: (column) => ColumnFilters(column),
  );

  ColumnFilters<DateTime> get createdAt => $composableBuilder(
    column: $table.createdAt,
    builder: (column) => ColumnFilters(column),
  );
}

class $$BloodSugarsTableOrderingComposer
    extends Composer<_$HealthDiaryDatabase, $BloodSugarsTable> {
  $$BloodSugarsTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<int> get id => $composableBuilder(
    column: $table.id,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<double> get value => $composableBuilder(
    column: $table.value,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<String> get note => $composableBuilder(
    column: $table.note,
    builder: (column) => ColumnOrderings(column),
  );

  ColumnOrderings<DateTime> get createdAt => $composableBuilder(
    column: $table.createdAt,
    builder: (column) => ColumnOrderings(column),
  );
}

class $$BloodSugarsTableAnnotationComposer
    extends Composer<_$HealthDiaryDatabase, $BloodSugarsTable> {
  $$BloodSugarsTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<int> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<double> get value =>
      $composableBuilder(column: $table.value, builder: (column) => column);

  GeneratedColumn<String> get note =>
      $composableBuilder(column: $table.note, builder: (column) => column);

  GeneratedColumn<DateTime> get createdAt =>
      $composableBuilder(column: $table.createdAt, builder: (column) => column);
}

class $$BloodSugarsTableTableManager
    extends
        RootTableManager<
          _$HealthDiaryDatabase,
          $BloodSugarsTable,
          BloodSugar,
          $$BloodSugarsTableFilterComposer,
          $$BloodSugarsTableOrderingComposer,
          $$BloodSugarsTableAnnotationComposer,
          $$BloodSugarsTableCreateCompanionBuilder,
          $$BloodSugarsTableUpdateCompanionBuilder,
          (
            BloodSugar,
            BaseReferences<
              _$HealthDiaryDatabase,
              $BloodSugarsTable,
              BloodSugar
            >,
          ),
          BloodSugar,
          PrefetchHooks Function()
        > {
  $$BloodSugarsTableTableManager(
    _$HealthDiaryDatabase db,
    $BloodSugarsTable table,
  ) : super(
        TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$BloodSugarsTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$BloodSugarsTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$BloodSugarsTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback:
              ({
                Value<int> id = const Value.absent(),
                Value<double> value = const Value.absent(),
                Value<String?> note = const Value.absent(),
                Value<DateTime> createdAt = const Value.absent(),
              }) => BloodSugarsCompanion(
                id: id,
                value: value,
                note: note,
                createdAt: createdAt,
              ),
          createCompanionCallback:
              ({
                Value<int> id = const Value.absent(),
                required double value,
                Value<String?> note = const Value.absent(),
                required DateTime createdAt,
              }) => BloodSugarsCompanion.insert(
                id: id,
                value: value,
                note: note,
                createdAt: createdAt,
              ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ),
      );
}

typedef $$BloodSugarsTableProcessedTableManager =
    ProcessedTableManager<
      _$HealthDiaryDatabase,
      $BloodSugarsTable,
      BloodSugar,
      $$BloodSugarsTableFilterComposer,
      $$BloodSugarsTableOrderingComposer,
      $$BloodSugarsTableAnnotationComposer,
      $$BloodSugarsTableCreateCompanionBuilder,
      $$BloodSugarsTableUpdateCompanionBuilder,
      (
        BloodSugar,
        BaseReferences<_$HealthDiaryDatabase, $BloodSugarsTable, BloodSugar>,
      ),
      BloodSugar,
      PrefetchHooks Function()
    >;

class $HealthDiaryDatabaseManager {
  final _$HealthDiaryDatabase _db;
  $HealthDiaryDatabaseManager(this._db);
  $$BloodPressuresTableTableManager get bloodPressures =>
      $$BloodPressuresTableTableManager(_db, _db.bloodPressures);
  $$BloodSugarsTableTableManager get bloodSugars =>
      $$BloodSugarsTableTableManager(_db, _db.bloodSugars);
}
