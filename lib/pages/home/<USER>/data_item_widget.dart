import 'package:flutter/material.dart';

/// 数据项显示组件
class DataItemWidget extends StatelessWidget {
  final String label;
  final String value;
  final String unit;

  const DataItemWidget({
    super.key,
    required this.label,
    required this.value,
    required this.unit,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        if (label.isNotEmpty) const SizedBox(height: 4),
        Text(
          value,
          style: Theme.of(context).textTheme.displayMedium,
        ),
        Text(
          unit,
          style: Theme.of(context).textTheme.labelSmall,
        ),
      ],
    );
  }
}