import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:health_diary/types/health_types.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'health_type_provider.g.dart';

class HealthType {
  List<HealthRecordTypeEnum> get types => HealthRecordTypeEnum.values;

  String getTypeName(HealthRecordTypeEnum type) {
    return type.name.tr();
  }
}

@riverpod
HealthType healthType(Ref ref) {
  return HealthType();
}
