// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_router.dart';

// **************************************************************************
// GoRouterGenerator
// **************************************************************************

List<RouteBase> get $appRoutes => [$mainFrameRoute];

RouteBase get $mainFrameRoute => GoRouteData.$route(
  path: '/',

  factory: _$MainFrameRoute._fromState,
  routes: [
    GoRouteData.$route(
      path: 'add-record/:type',

      factory: _$AddRecordRoute._fromState,
    ),
  ],
);

mixin _$MainFrameRoute on GoRouteData {
  static MainFrameRoute _fromState(GoRouterState state) =>
      const MainFrameRoute();

  @override
  String get location => GoRouteData.$location('/');

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}

mixin _$AddRecordRoute on GoRouteData {
  static AddRecordRoute _fromState(GoRouterState state) =>
      AddRecordRoute(type: state.pathParameters['type']!);

  AddRecordRoute get _self => this as AddRecordRoute;

  @override
  String get location =>
      GoRouteData.$location('/add-record/${Uri.encodeComponent(_self.type)}');

  @override
  void go(BuildContext context) => context.go(location);

  @override
  Future<T?> push<T>(BuildContext context) => context.push<T>(location);

  @override
  void pushReplacement(BuildContext context) =>
      context.pushReplacement(location);

  @override
  void replace(BuildContext context) => context.replace(location);
}
