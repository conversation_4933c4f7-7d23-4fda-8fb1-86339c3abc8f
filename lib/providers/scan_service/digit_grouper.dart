part of 'scan_provider.dart';

class DisjointSet {
  final List<int> parent;

  DisjointSet(int n) : parent = List<int>.generate(n, (i) => i);

  int find(int i) {
    if (parent[i] == i) {
      return i;
    }
    parent[i] = find(parent[i]);
    return parent[i];
  }

  bool union(int i, int j) {
    final rootI = find(i);
    final rootJ = find(j);
    if (rootI != rootJ) {
      parent[rootJ] = rootI;
      return true;
    }
    return false;
  }
}

/// 数字分组器配置
class DigitGrouperConfig {
  /// 两个数字之间允许的最大水平间隙（像素）
  final double gapThreshold;
  /// 两个数字在垂直方向上的最小重叠比例（0.0到1.0）
  final double yOverlapThreshold;
  /// 用于判断两个组是否在"同一行"的Y坐标容忍度（像素）
  final double ySortTolerance;
  
  const DigitGrouperConfig({
    this.gapThreshold = 40.0,
    this.yOverlapThreshold = 0.7,
    this.ySortTolerance = 5.0,
  });
}

/// 数字分组器
class DigitGrouper {
  final DigitGrouperConfig _config;
  
  const DigitGrouper([this._config = const DigitGrouperConfig()]);

  /// 计算两个边界框之间的水平间隙。
  /// 如果 Box 有水平重叠，间隙为0。
  double _calculateHorizontalGap(Rect box1, Rect box2) {
    final minX1 = box1.left;
    final maxX1 = box1.right;
    final minX2 = box2.left;
    final maxX2 = box2.right;

    if (maxX1 < minX2) { // box1 在 box2 左边，且没有水平重叠
      return minX2 - maxX1;
    } else if (maxX2 < minX1) { // box2 在 box1 左边，且没有水平重叠
      return minX1 - maxX2;
    } else { // 两个 Box 有水平重叠
      return 0.0;
    }
  }

  /// 计算两个边界框在垂直方向上的重叠比例。
  /// 返回值介于 0.0 到 1.0 之间。
  double _calculateVerticalOverlapRatio(Rect box1, Rect box2) {
    final minY1 = box1.top;
    final maxY1 = box1.bottom;
    final minY2 = box2.top;
    final maxY2 = box2.bottom;

    final yOverlap =
        (min(maxY1, maxY2) - max(minY1, minY2)).clamp(0.0, double.infinity); // 确保非负

    final height1 = box1.height; // 直接使用 Rect 的 height getter
    final height2 = box2.height; // 直接使用 Rect 的 height getter
    final minHeight = min(height1, height2);

    if (minHeight > 0) {
      return yOverlap / minHeight;
    }
    return 0.0;
  }

  /// 执行数字分组并最终排序的主函数。
  /// [detections]：包含每个 Detection 对象的列表。
  /// 返回：包含每个数字整体的列表，每个整体是一个按顺序排列的数字字符串。
  List<int> groupAndSort(List<Detection> detections) {
    if (detections.isEmpty) {
      return [];
    }

    final n = detections.length;
    final dsu = DisjointSet(n);

    // 1. 构建连通图：遍历所有数字对并根据条件合并集合
    for (int i = 0; i < n; i++) {
      for (int j = i + 1; j < n; j++) {
        final box1 = detections[i].boundingBox; // 使用 boundingBox 属性
        final box2 = detections[j].boundingBox; // 使用 boundingBox 属性

        // 首先检查垂直对齐
        final overlapRatio = _calculateVerticalOverlapRatio(box1, box2);
        if (overlapRatio <= _config.yOverlapThreshold) {
          continue; // 垂直对齐不满足，跳过
        }

        // 垂直对齐满足后，检查水平间隙
        final horizontalGap = _calculateHorizontalGap(box1, box2);
        if (horizontalGap <= _config.gapThreshold) {
          dsu.union(i, j);
        }
      }
    }

    // 2. 收集原始分组数据
    final Map<int, List<Detection>> groupsRaw = {}; // 类型改为 List<Detection>
    for (int i = 0; i < n; i++) {
      final root = dsu.find(i);
      groupsRaw.putIfAbsent(root, () => []).add(detections[i]);
    }

    // 3. 整合并准备最终排序数据
    final List<_GroupInfo> finalGroupedResults = [];
    groupsRaw.forEach((root, currentGroupMembers) {
      // 对组内数字按 minX 排序，确保拼接顺序正确
      currentGroupMembers.sort((a, b) => a.boundingBox.left.compareTo(b.boundingBox.left));
      int combinedValue = 0;
      for (final detection in currentGroupMembers) {
        combinedValue = combinedValue * 10 + detection.classId;
      }

      // 计算整个组的代表性边界框，用于后续的排序
      // 直接使用 Rect 的 left, top, right, bottom 属性
      final groupMinX = currentGroupMembers.map((d) => d.boundingBox.left).reduce(min);
      final groupMinY = currentGroupMembers.map((d) => d.boundingBox.top).reduce(min);
      final groupMaxY = currentGroupMembers.map((d) => d.boundingBox.bottom).reduce(max);

      // 计算组的中心 Y 坐标和最小 X 坐标作为排序键
      final groupCenterY = (groupMinY + groupMaxY) / 2;
      final groupStartX = groupMinX;

      finalGroupedResults.add(_GroupInfo(
        value: combinedValue,
        centerY: groupCenterY,
        startX: groupStartX,
      ));
    });

    // 4. 最终排序：从上到下，从左到右
    finalGroupedResults.sort((a, b) {
      final roundedAY = (a.centerY / _config.ySortTolerance).round() * _config.ySortTolerance;
      final roundedBY = (b.centerY / _config.ySortTolerance).round() * _config.ySortTolerance;

      if (roundedAY != roundedBY) {
        return roundedAY.compareTo(roundedBY);
      }
      return a.startX.compareTo(b.startX);
    });

    // 提取最终的字符串结果
    return finalGroupedResults.map((info) => info.value).toList();
  }
}


class _GroupInfo {
  final int value;
  final double centerY;
  final double startX;

  _GroupInfo({required this.value, required this.centerY, required this.startX});
}
