// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'recent_records_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$recentRecordsControllerHash() =>
    r'629852bedc37b3110f8eb1f15f8ff5c65f8b022a';

/// 最近记录控制器
///
/// Copied from [RecentRecordsController].
@ProviderFor(RecentRecordsController)
final recentRecordsControllerProvider =
    AutoDisposeAsyncNotifierProvider<
      RecentRecordsController,
      List<HealthRecordEntry>
    >.internal(
      RecentRecordsController.new,
      name: r'recentRecordsControllerProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$recentRecordsControllerHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$RecentRecordsController =
    AutoDisposeAsyncNotifier<List<HealthRecordEntry>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
