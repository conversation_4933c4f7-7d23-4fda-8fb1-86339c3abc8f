import 'package:camera/camera.dart';
import 'package:flutter/foundation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:easy_localization/easy_localization.dart';

part 'camera_provider.g.dart';

@riverpod
class CameraControllerNotifier extends _$CameraControllerNotifier {
  CameraController? _controller;

  @override
  Future<CameraController?> build() async {
    // 当provider被销毁时，清理相机资源
    ref.onDispose(() {
      _disposeCamera();
    });

    final cameras = await availableCameras();
    if (cameras.isEmpty) {
      debugPrint('No camera available');
      throw CameraException('no_camera_available', 'no_camera_available'.tr());
    }

    _controller = CameraController(
      cameras[0],
      ResolutionPreset.high,
      enableAudio: false,
      imageFormatGroup: ImageFormatGroup.jpeg,
    );

    await _controller!.initialize();
    _controller!.setFocusMode(FocusMode.auto);
    _controller!.setZoomLevel(1.2);

    return _controller;
  }

  void _disposeCamera() {
    _controller?.dispose();
    _controller = null;
  }
}
